/**
 * 智能讲解助手 - 后台脚本
 * 负责主页面与iframe内容脚本之间的通信桥梁
 */

console.log('🚀 智能讲解助手后台脚本启动');

// 存储活动的标签页和通信状态
const activeTabs = new Map();
const pendingRequests = new Map();

/**
 * 监听来自网页的消息
 */
chrome.runtime.onMessageExternal.addListener((message, sender, sendResponse) => {
    console.log('📨 收到外部消息:', message, '来源:', sender);
    
    if (message.type === 'PRESENTATION_ACTION') {
        handlePresentationAction(message, sender, sendResponse);
        return true; // 保持消息通道开放
    }
    
    if (message.type === 'PING') {
        sendResponse({ type: 'PONG', success: true });
        return true;
    }
});

/**
 * 监听来自内容脚本的消息
 */
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('📨 收到内容脚本消息:', message, '来源:', sender);
    
    if (message.type === 'CONTENT_SCRIPT_READY') {
        // 内容脚本准备就绪
        if (sender.tab) {
            activeTabs.set(sender.tab.id, {
                tabId: sender.tab.id,
                frameId: sender.frameId,
                url: sender.url,
                ready: true
            });
            console.log('✅ 内容脚本就绪:', sender.tab.id, sender.frameId);
        }
        sendResponse({ success: true });
        return true;
    }
    
    if (message.type === 'PRESENTATION_RESPONSE') {
        // 转发响应给等待的请求
        const requestId = message.requestId;
        if (pendingRequests.has(requestId)) {
            const { sendResponse: originalSendResponse } = pendingRequests.get(requestId);
            originalSendResponse(message);
            pendingRequests.delete(requestId);
        }
        return true;
    }
});

/**
 * 处理智能讲解操作请求
 */
async function handlePresentationAction(message, sender, sendResponse) {
    try {
        console.log('🎯 处理讲解操作:', message.action);
        
        // 生成请求ID
        const requestId = generateRequestId();
        message.requestId = requestId;
        
        // 存储响应回调
        pendingRequests.set(requestId, { sendResponse });
        
        // 查找目标标签页
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tabs.length === 0) {
            throw new Error('未找到活动标签页');
        }
        
        const targetTab = tabs[0];
        console.log('🎯 目标标签页:', targetTab.id);
        
        // 发送消息到所有框架（包括iframe）
        try {
            await chrome.tabs.sendMessage(targetTab.id, message);
            console.log('✅ 消息已发送到内容脚本');
        } catch (error) {
            console.error('❌ 发送消息失败:', error);
            sendResponse({
                type: 'PRESENTATION_RESPONSE',
                success: false,
                error: '无法与页面通信，请确保页面已完全加载'
            });
        }
        
    } catch (error) {
        console.error('❌ 处理操作失败:', error);
        sendResponse({
            type: 'PRESENTATION_RESPONSE',
            success: false,
            error: error.message
        });
    }
}

/**
 * 生成唯一请求ID
 */
function generateRequestId() {
    return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

/**
 * 监听标签页更新
 */
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete') {
        console.log('📄 标签页加载完成:', tabId, tab.url);
    }
});

/**
 * 监听标签页关闭
 */
chrome.tabs.onRemoved.addListener((tabId) => {
    activeTabs.delete(tabId);
    console.log('🗑️ 清理标签页数据:', tabId);
});

/**
 * 扩展安装时的初始化
 */
chrome.runtime.onInstalled.addListener((details) => {
    console.log('🎉 智能讲解助手已安装/更新:', details.reason);
    
    if (details.reason === 'install') {
        // 首次安装时的设置
        chrome.storage.local.set({
            enabled: true,
            version: '1.0.0',
            installTime: Date.now()
        });
    }
});
