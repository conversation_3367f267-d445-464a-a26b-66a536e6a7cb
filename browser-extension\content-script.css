/* 智能讲解助手 - Content Script 样式 */

/* 高亮效果样式 */
.intelligent-presentation-highlight {
    position: relative;
    outline: 3px solid var(--highlight-color, #ff0000) !important;
    background-color: var(--highlight-bg, rgba(255, 0, 0, 0.1)) !important;
    box-shadow: 0 0 10px var(--highlight-color, #ff0000) !important;
    z-index: 9999 !important;
    transition: all 0.3s ease !important;
}

/* 点击指示器样式 */
.intelligent-presentation-click-indicator {
    position: relative;
    animation: click-pulse 0.6s ease-out !important;
    z-index: 9999 !important;
}

@keyframes click-pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(0, 123, 255, 0.3);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 20px rgba(0, 123, 255, 0);
    }
}

/* 扩展标记样式（用于调试） */
#extension-marker {
    position: fixed;
    top: 10px;
    right: 10px;
    background: #28a745;
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 12px;
    z-index: 10000;
    font-family: Arial, sans-serif;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

/* 确保高亮元素在所有情况下都可见 */
.intelligent-presentation-highlight::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border: 3px solid var(--highlight-color, #ff0000);
    border-radius: 3px;
    pointer-events: none;
    z-index: 9999;
}

/* 针对特殊元素的高亮优化 */
.intelligent-presentation-highlight input,
.intelligent-presentation-highlight button,
.intelligent-presentation-highlight select,
.intelligent-presentation-highlight textarea {
    border-color: var(--highlight-color, #ff0000) !important;
}

/* 确保高亮效果不被其他样式覆盖 */
.intelligent-presentation-highlight * {
    position: relative !important;
}
