// 主世界桥接脚本 - 直接在页面的主世界上下文中运行
console.log('🌍 主世界桥接脚本开始执行...');

// 定义桥接API
window.intelligentPresentationExtensionBridge = window.intelligentPresentationExtensionBridge || {};

// 添加input方法
window.intelligentPresentationExtensionBridge.input = function(selector, value, options = {}) {
    return new Promise((resolve, reject) => {
        window.postMessage({
            type: 'INTELLIGENT_PRESENTATION_BRIDGE',
            action: 'input',
            selector,
            options: { ...options, value }
        }, '*');
        // 简单resolve，实际可扩展为监听回执
        setTimeout(resolve, 100);
    });
};

// 添加其他方法
window.intelligentPresentationExtensionBridge.highlight = function(selector, options) {
    console.log('🎯 桥接API调用: highlight', selector, options);
    
    // 发送消息到content script
    window.postMessage({
        type: 'INTELLIGENT_PRESENTATION_BRIDGE',
        action: 'highlight',
        selector: selector,
        options: options || {}
    }, '*');
    
    return { success: true };
};

window.intelligentPresentationExtensionBridge.click = function(selector, options) {
    console.log('🎯 桥接API调用: click', selector, options);
    
    // 发送消息到content script
    window.postMessage({
        type: 'INTELLIGENT_PRESENTATION_BRIDGE',
        action: 'click',
        selector: selector,
        options: options || {}
    }, '*');
    
    return { success: true };
};

window.intelligentPresentationExtensionBridge.simulateClick = function(selector, options) {
    console.log('🎯 桥接API调用: simulateClick', selector, options);
    
    // 发送消息到content script
    window.postMessage({
        type: 'INTELLIGENT_PRESENTATION_BRIDGE',
        action: 'simulateClick',
        selector: selector,
        options: options || {}
    }, '*');
    
    return { success: true };
};

window.intelligentPresentationExtensionBridge.clearHighlight = function() {
    console.log('🎯 桥接API调用: clearHighlight');
    
    // 发送消息到content script
    window.postMessage({
        type: 'INTELLIGENT_PRESENTATION_BRIDGE',
        action: 'clearHighlight'
    }, '*');
    
    return { success: true };
};

window.intelligentPresentationExtensionBridge.getInfo = function() {
    console.log('🎯 桥接API调用: getInfo');
    
    // 发送消息到content script
    window.postMessage({
        type: 'INTELLIGENT_PRESENTATION_BRIDGE',
        action: 'getInfo'
    }, '*');
    
    return { success: true };
};

console.log('✅ 桥接API已在页面主世界中创建');
console.log('🔍 验证桥接API:', window.intelligentPresentationExtensionBridge);

// 发送准备好的事件
window.dispatchEvent(new CustomEvent('intelligentPresentationBridgeReady', {
    detail: { ready: true }
}));

console.log('🎉 桥接API准备完成，已发送准备事件');
