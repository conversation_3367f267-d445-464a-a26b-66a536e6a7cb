/**
 * 错误处理器 - 负责讲解过程中的错误处理和容错机制
 */
class ErrorHandler {
    constructor() {
        this.maxRetries = 3;
        this.retryDelay = 1000;
        this.errorLog = [];
    }

    /**
     * 处理错误
     * @param {Error} error - 错误对象
     * @param {Object} step - 当前执行的步骤
     * @param {PresentationController} controller - 讲解控制器实例
     */
    async handle(error, step, controller) {
        console.error('讲解步骤执行错误:', error);
        
        // 记录错误
        this.logError(error, step);

        // 根据错误类型决定处理策略
        const strategy = this.getErrorStrategy(error, step);
        
        try {
            await this.executeStrategy(strategy, error, step, controller);
        } catch (handlingError) {
            console.error('错误处理失败:', handlingError);
            await this.handleCriticalError(handlingError, step, controller);
        }
    }

    /**
     * 记录错误信息
     */
    logError(error, step) {
        const errorInfo = {
            timestamp: new Date().toISOString(),
            stepId: step.id,
            stepAction: step.action,
            stepTarget: step.target,
            errorMessage: error.message,
            errorStack: error.stack
        };
        
        this.errorLog.push(errorInfo);
        
        // 保持错误日志大小
        if (this.errorLog.length > 100) {
            this.errorLog.shift();
        }
    }

    /**
     * 获取错误处理策略
     */
    getErrorStrategy(error, step) {
        const errorMessage = error.message.toLowerCase();
        
        // 元素未找到错误
        if (errorMessage.includes('找不到元素') || 
            errorMessage.includes('element not found') ||
            errorMessage.includes('cannot read property')) {
            return 'ELEMENT_NOT_FOUND';
        }
        
        // 超时错误
        if (errorMessage.includes('timeout') || 
            errorMessage.includes('超时')) {
            return 'TIMEOUT';
        }
        
        // 网络错误
        if (errorMessage.includes('network') || 
            errorMessage.includes('fetch') ||
            errorMessage.includes('网络')) {
            return 'NETWORK_ERROR';
        }
        
        // 语音错误
        if (errorMessage.includes('speech') || 
            errorMessage.includes('语音')) {
            return 'SPEECH_ERROR';
        }
        
        // 点击错误
        if (step.action === 'click' && 
            (errorMessage.includes('click') || errorMessage.includes('点击'))) {
            return 'CLICK_ERROR';
        }
        
        // 默认策略
        return 'GENERAL_ERROR';
    }

    /**
     * 执行错误处理策略
     */
    async executeStrategy(strategy, error, step, controller) {
        switch (strategy) {
            case 'ELEMENT_NOT_FOUND':
                await this.handleElementNotFound(error, step, controller);
                break;
                
            case 'TIMEOUT':
                await this.handleTimeout(error, step, controller);
                break;
                
            case 'NETWORK_ERROR':
                await this.handleNetworkError(error, step, controller);
                break;
                
            case 'SPEECH_ERROR':
                await this.handleSpeechError(error, step, controller);
                break;
                
            case 'CLICK_ERROR':
                await this.handleClickError(error, step, controller);
                break;
                
            case 'GENERAL_ERROR':
            default:
                await this.handleGeneralError(error, step, controller);
                break;
        }
    }

    /**
     * 处理元素未找到错误
     */
    async handleElementNotFound(error, step, controller) {
        console.log('处理元素未找到错误...');
        
        // 尝试等待元素出现
        const waitTime = 2000;
        controller.uiController.showText(`正在等待页面元素加载... (${step.text})`);
        
        await this.sleep(waitTime);
        
        // 尝试使用备用选择器
        if (step.fallbackTarget) {
            console.log('尝试使用备用选择器:', step.fallbackTarget);
            const originalTarget = step.target;
            step.target = step.fallbackTarget;
            
            try {
                await controller.executeStep(step);
                return; // 成功执行
            } catch (fallbackError) {
                console.log('备用选择器也失败了');
                step.target = originalTarget; // 恢复原始目标
            }
        }
        
        // 如果仍然失败，跳过当前步骤
        controller.uiController.showText(`无法找到页面元素，跳过当前步骤: ${step.text}`);
        await this.sleep(2000);
    }

    /**
     * 处理超时错误
     */
    async handleTimeout(error, step, controller) {
        console.log('处理超时错误...');
        
        // 显示超时提示
        controller.uiController.showText(`操作超时，正在重试... (${step.text})`);
        
        // 增加超时时间重试
        if (step.timeout) {
            step.timeout = Math.min(step.timeout * 1.5, 10000); // 最大10秒
        }
        
        await this.sleep(1000);
        
        // 重试执行
        try {
            await controller.executeStep(step);
        } catch (retryError) {
            console.log('重试仍然超时，跳过步骤');
            controller.uiController.showText(`操作超时，跳过当前步骤: ${step.text}`);
            await this.sleep(2000);
        }
    }

    /**
     * 处理网络错误
     */
    async handleNetworkError(error, step, controller) {
        console.log('处理网络错误...');
        
        controller.uiController.showText('网络连接异常，正在重试...');
        
        // 等待网络恢复
        await this.sleep(3000);
        
        // 检查网络连接
        if (navigator.onLine) {
            try {
                await controller.executeStep(step);
            } catch (retryError) {
                controller.uiController.showText('网络问题持续，跳过当前步骤');
                await this.sleep(2000);
            }
        } else {
            controller.uiController.showText('网络连接断开，请检查网络后重新开始');
            controller.pause();
        }
    }

    /**
     * 处理语音错误
     */
    async handleSpeechError(error, step, controller) {
        console.log('处理语音错误...');
        
        // 禁用当前步骤的语音
        step.speech = false;
        
        controller.uiController.showText(`语音播放失败，继续文字展示: ${step.text}`);
        
        // 继续执行其他操作
        try {
            await controller.executeStep(step);
        } catch (executeError) {
            console.log('步骤执行也失败了');
            await this.handleGeneralError(executeError, step, controller);
        }
    }

    /**
     * 处理点击错误
     */
    async handleClickError(error, step, controller) {
        console.log('处理点击错误...');
        
        controller.uiController.showText(`点击操作失败，尝试其他方式... (${step.text})`);
        
        // 尝试不同的点击方式
        const element = controller.findElement(step.target);
        if (element) {
            try {
                // 尝试触发事件
                element.dispatchEvent(new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                }));
                
                await this.sleep(1000);
                return;
            } catch (eventError) {
                console.log('事件触发也失败了');
            }
        }
        
        // 跳过点击，继续后续步骤
        controller.uiController.showText(`无法执行点击操作，跳过当前步骤: ${step.text}`);
        await this.sleep(2000);
    }

    /**
     * 处理一般错误
     */
    async handleGeneralError(error, step, controller) {
        console.log('处理一般错误...');
        
        controller.uiController.showText(`执行出现问题: ${error.message}`);
        
        // 尝试重试
        if (!step._retryCount) {
            step._retryCount = 0;
        }
        
        if (step._retryCount < this.maxRetries) {
            step._retryCount++;
            controller.uiController.showText(`正在重试 (${step._retryCount}/${this.maxRetries})...`);
            
            await this.sleep(this.retryDelay);
            
            try {
                await controller.executeStep(step);
                return;
            } catch (retryError) {
                console.log(`重试 ${step._retryCount} 失败:`, retryError);
            }
        }
        
        // 重试次数用完，跳过步骤
        controller.uiController.showText(`多次重试失败，跳过当前步骤: ${step.text}`);
        await this.sleep(2000);
    }

    /**
     * 处理严重错误
     */
    async handleCriticalError(error, step, controller) {
        console.error('严重错误，停止讲解:', error);
        
        controller.uiController.showText('发生严重错误，讲解已停止');
        controller.uiController.showMessage('讲解过程中发生严重错误', 'error');
        
        // 等待一下再停止
        await this.sleep(3000);
        controller.stop();
    }

    /**
     * 获取错误统计
     */
    getErrorStats() {
        const stats = {
            totalErrors: this.errorLog.length,
            errorsByType: {},
            errorsByStep: {},
            recentErrors: this.errorLog.slice(-10)
        };
        
        this.errorLog.forEach(error => {
            // 按错误类型统计
            const errorType = this.getErrorStrategy({ message: error.errorMessage }, { action: error.stepAction });
            stats.errorsByType[errorType] = (stats.errorsByType[errorType] || 0) + 1;
            
            // 按步骤统计
            stats.errorsByStep[error.stepId] = (stats.errorsByStep[error.stepId] || 0) + 1;
        });
        
        return stats;
    }

    /**
     * 清除错误日志
     */
    clearErrorLog() {
        this.errorLog = [];
    }

    /**
     * 工具函数：延时
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 设置重试参数
     */
    setRetryConfig(maxRetries, retryDelay) {
        this.maxRetries = maxRetries;
        this.retryDelay = retryDelay;
    }
}

// 暴露到全局作用域
window.ErrorHandler = ErrorHandler;
