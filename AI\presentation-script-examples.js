/**
 * 智能讲解脚本示例集合
 * 展示各种参数的实际使用方法
 */

// 示例1: 基础高亮操作
const basicHighlightExample = {
    id: 1,
    action: 'highlight',                    // 操作类型：高亮
    target: '.project-intro-panel',         // 目标元素：项目介绍面板
    text: '这是项目介绍面板',              // 显示文本
    duration: 3000                          // 持续时间：3秒
};

// 示例2: 带语音选项的高亮
const highlightWithSpeechExample = {
    id: 2,
    action: 'highlight',
    target: '#map',
    text: '这是智能地图区域，可以查看各地区的监测设备分布。',
    duration: 4000,
    speechOptions: {                        // 语音控制选项
        rate: 0.8,                         // 语速：0.8倍
        pitch: 1.1,                        // 音调：1.1倍
        volume: 0.9                        // 音量：0.9倍
    }
};

// 示例3: 复杂目标选择的点击操作
const complexClickExample = {
    id: 3,
    action: 'click',
    target: {                              // 复杂目标对象
        selector: '#leaflet-tooltip-208',   // 主选择器
        xpath: '/html/body/div[2]/div/div[7]/div[1]/div[5]/div[6]', // XPath备选
        text: '墨竹工卡县',                 // 文本匹配
        fallbackTarget: '#map'             // 备用目标
    },
    text: '点击墨竹工卡县进入详细页面',
    duration: 2000,
    options: {                             // 操作选项
        waitFor: '#device-legend-panel',   // 等待元素出现
        timeout: 8000                      // 超时时间：8秒
    }
};

// 示例4: 页面导航操作
const navigationExample = {
    id: 4,
    action: 'navigate',                    // 操作类型：导航
    target: 'realtime.html',              // 目标URL
    text: '正在跳转到实时监控页面...',
    duration: 3000,
    options: {
        waitFor: '.realtime-container',    // 等待新页面元素
        timeout: 10000                     // 导航超时：10秒
    }
};

// 示例5: 文本输入操作
const inputExample = {
    id: 5,
    action: 'input',                       // 操作类型：输入
    target: '#search-input',               // 输入框选择器
    value: '墨竹工卡县',                   // 输入值
    text: '在搜索框中输入县名',
    duration: 2000,
    options: {
        clearFirst: true,                  // 先清空输入框
        typeSpeed: 100,                    // 打字速度：100ms/字符
        submitAfter: true                  // 输入后按回车
    }
};

// 示例6: 等待操作
const waitExample = {
    id: 6,
    action: 'wait',                        // 操作类型：等待
    target: '.loading-spinner',            // 等待的元素
    text: '正在加载数据，请稍候...',
    timeout: 8000,                         // 等待超时：8秒
    duration: 1000,
    options: {
        waitForDisappear: true             // 等待元素消失
    }
};

// 示例7: 滚动操作
const scrollExample = {
    id: 7,
    action: 'scroll',                      // 操作类型：滚动
    target: '.device-list',                // 滚动目标
    text: '滚动查看更多设备信息',
    duration: 2000,
    options: {
        behavior: 'smooth',                // 平滑滚动
        block: 'center',                   // 垂直居中
        inline: 'nearest',                 // 水平就近
        offset: { top: -100 }              // 额外偏移
    }
};

// 示例8: 自定义高亮样式
const customHighlightExample = {
    id: 8,
    action: 'highlight',
    target: '.special-element',
    text: '这是一个特殊的高亮效果',
    duration: 3000,
    options: {
        highlightStyle: {                  // 自定义高亮样式
            borderColor: '#ff6b6b',        // 边框颜色
            borderWidth: '4px',            // 边框宽度
            boxShadow: '0 0 30px rgba(255, 107, 107, 0.8)', // 阴影
            animation: 'pulse 1.5s infinite' // 动画效果
        },
        overlayOpacity: 0.3                // 遮罩透明度
    }
};

// 示例9: 条件执行
const conditionalExample = {
    id: 9,
    action: 'click',
    target: '.optional-button',
    text: '如果按钮存在则点击',
    duration: 2000,
    options: {
        skipIfNotFound: true,              // 找不到时跳过
        retryCount: 3,                     // 重试次数
        retryDelay: 1000                   // 重试间隔：1秒
    }
};

// 示例10: 禁用语音
const noSpeechExample = {
    id: 10,
    action: 'highlight',
    target: '.silent-element',
    text: '这个步骤不播放语音',
    duration: 2000,
    speech: false                          // 禁用语音播放
};

// 示例11: 双击操作
const doubleClickExample = {
    id: 11,
    action: 'click',
    target: '.double-click-target',
    text: '双击打开详细信息',
    duration: 2000,
    options: {
        doubleClick: true,                 // 启用双击
        waitAfterClick: 1500               // 点击后等待1.5秒
    }
};

// 示例12: 多重选择器
const multiSelectorExample = {
    id: 12,
    action: 'click',
    target: '.primary-btn, .secondary-btn, #fallback-btn', // 多个选择器
    text: '点击任一可用按钮',
    duration: 2000
};

// 示例13: 属性选择器
const attributeSelectorExample = {
    id: 13,
    action: 'click',
    target: 'button[data-type="weather"][data-status="online"]', // 属性选择器
    text: '点击在线的气象监测设备',
    duration: 2000
};

// 示例14: 伪类选择器
const pseudoSelectorExample = {
    id: 14,
    action: 'highlight',
    target: '.device-list .device-item:first-child', // 伪类选择器
    text: '高亮第一个设备项',
    duration: 2000
};

// 完整的示例脚本
const exampleScript = [
    basicHighlightExample,
    highlightWithSpeechExample,
    complexClickExample,
    navigationExample,
    inputExample,
    waitExample,
    scrollExample,
    customHighlightExample,
    conditionalExample,
    noSpeechExample,
    doubleClickExample,
    multiSelectorExample,
    attributeSelectorExample,
    pseudoSelectorExample
];

// 导出示例供参考
if (typeof window !== 'undefined') {
    window.presentationExamples = {
        basicHighlightExample,
        highlightWithSpeechExample,
        complexClickExample,
        navigationExample,
        inputExample,
        waitExample,
        scrollExample,
        customHighlightExample,
        conditionalExample,
        noSpeechExample,
        doubleClickExample,
        multiSelectorExample,
        attributeSelectorExample,
        pseudoSelectorExample,
        exampleScript
    };
}

// 使用方法示例：
// 1. 替换当前脚本：window.presentationScript = exampleScript;
// 2. 添加单个步骤：window.presentationScript.push(basicHighlightExample);
// 3. 修改现有步骤：window.presentationScript[0] = highlightWithSpeechExample;
