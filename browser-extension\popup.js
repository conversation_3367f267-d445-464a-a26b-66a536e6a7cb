/**
 * 智能讲解助手 - 弹窗脚本
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('🎯 智能讲解助手弹窗加载');
    
    const statusElement = document.getElementById('status');
    const statusText = document.getElementById('status-text');
    const testBtn = document.getElementById('test-btn');
    const clearBtn = document.getElementById('clear-btn');
    const helpBtn = document.getElementById('help-btn');
    
    // 检查插件状态
    checkExtensionStatus();
    
    // 绑定事件
    testBtn.addEventListener('click', testFunction);
    clearBtn.addEventListener('click', clearHighlights);
    helpBtn.addEventListener('click', showHelp);
    
    /**
     * 检查插件状态
     */
    async function checkExtensionStatus() {
        try {
            // 获取当前活动标签页
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tabs.length === 0) {
                updateStatus(false, '未找到活动标签页');
                return;
            }
            
            const tab = tabs[0];
            
            // 检查是否可以访问标签页
            if (tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                updateStatus(false, '无法在此页面使用');
                return;
            }
            
            // 检查内容脚本是否注入
            try {
                const results = await chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    func: () => {
                        // 检查多个标记
                        const hasExtensionFlag = window.intelligentPresentationExtension === true;
                        const hasMarker = document.getElementById('extension-marker') !== null;
                        return {
                            hasFlag: hasExtensionFlag,
                            hasMarker: hasMarker,
                            url: window.location.href
                        };
                    }
                });

                const result = results && results[0] ? results[0].result : null;
                if (result && (result.hasFlag || result.hasMarker)) {
                    updateStatus(true, '插件运行正常');
                    console.log('检测结果:', result);
                } else {
                    updateStatus(false, '内容脚本未就绪');
                    console.log('检测失败:', result);
                }
            } catch (error) {
                updateStatus(false, '内容脚本未就绪: ' + error.message);
                console.error('检测错误:', error);
            }
            
        } catch (error) {
            console.error('检查状态失败:', error);
            updateStatus(false, '状态检查失败');
        }
    }
    
    /**
     * 更新状态显示
     */
    function updateStatus(isActive, message) {
        statusElement.className = `status ${isActive ? 'active' : 'inactive'}`;
        statusText.textContent = message;
        
        // 根据状态启用/禁用按钮
        testBtn.disabled = !isActive;
        clearBtn.disabled = !isActive;
    }
    
    /**
     * 测试功能
     */
    async function testFunction() {
        try {
            testBtn.disabled = true;
            testBtn.textContent = '测试中...';
            
            // 获取当前标签页
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            const tab = tabs[0];

            // 直接在页面中执行测试
            const results = await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                func: () => {
                    if (window.intelligentPresentationExtension) {
                        return {
                            success: true,
                            data: {
                                title: document.title,
                                url: window.location.href,
                                isIframe: window !== window.top
                            }
                        };
                    } else {
                        return { success: false, error: '插件未加载' };
                    }
                }
            });

            const response = results && results[0] ? results[0].result : { success: false, error: '执行失败' };
            
            if (response && response.success) {
                alert('✅ 功能测试成功！\n\n页面信息：\n' + 
                      `标题: ${response.data.data.title}\n` +
                      `URL: ${response.data.data.url}\n` +
                      `是否为iframe: ${response.data.data.isIframe ? '是' : '否'}`);
            } else {
                alert('❌ 功能测试失败：' + (response?.error || '未知错误'));
            }
            
        } catch (error) {
            console.error('测试失败:', error);
            alert('❌ 测试失败：' + error.message);
        } finally {
            testBtn.disabled = false;
            testBtn.textContent = '测试功能';
        }
    }
    
    /**
     * 清除高亮
     */
    async function clearHighlights() {
        try {
            clearBtn.disabled = true;
            clearBtn.textContent = '清除中...';
            
            // 获取当前标签页
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            const tab = tabs[0];

            // 直接在页面中执行清除高亮
            const results = await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                func: () => {
                    // 移除所有高亮样式
                    const highlightedElements = document.querySelectorAll('.intelligent-presentation-highlight');
                    highlightedElements.forEach(el => {
                        el.classList.remove('intelligent-presentation-highlight');
                        el.style.removeProperty('--highlight-color');
                    });
                    return { success: true };
                }
            });

            const response = results && results[0] ? results[0].result : { success: false };
            
            if (response && response.success) {
                console.log('✅ 高亮已清除');
            } else {
                console.error('❌ 清除高亮失败:', response?.error);
            }
            
        } catch (error) {
            console.error('清除高亮失败:', error);
        } finally {
            clearBtn.disabled = false;
            clearBtn.textContent = '清除高亮';
        }
    }
    
    /**
     * 显示帮助
     */
    function showHelp() {
        const helpText = `
智能讲解助手使用说明：

1. 安装插件后，访问包含iframe的页面
2. 插件会自动注入到所有页面和iframe中
3. 主页面可以通过插件API操作iframe中的元素

支持的操作：
• 高亮元素
• 点击元素  
• 滚动到元素
• 获取页面信息
• 清除高亮

API使用示例：
chrome.runtime.sendMessage({
    type: 'PRESENTATION_ACTION',
    action: 'highlight',
    params: {
        selector: '.target-element',
        color: '#ff0000',
        duration: 3000
    }
});

如有问题，请联系技术支持。
        `;
        
        alert(helpText);
    }
    
    // 定期检查状态
    setInterval(checkExtensionStatus, 5000);
});
