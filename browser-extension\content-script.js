/**
 * 智能讲解助手 - 内容脚本
 * 在页面中执行具体的DOM操作
 */

// 立即执行，设置扩展标记
console.log('🎯 智能讲解助手内容脚本开始加载:', window.location.href);

// 避免重复注入
if (window.intelligentPresentationExtension) {
    console.log('⚠️ 内容脚本已存在，跳过注入');
} else {
    window.intelligentPresentationExtension = true;
    console.log('✅ 扩展标记已设置');

    // 立即可用的简单功能
    
    // 高亮样式管理
    const highlightManager = {
        highlights: new Map(),
        styleElement: null,
        
        init() {
            this.createStyles();
        },
        
        createStyles() {
            if (this.styleElement) return;
            
            this.styleElement = document.createElement('style');
            this.styleElement.textContent = `
                .intelligent-presentation-highlight {
                    position: relative !important;
                    z-index: 9999 !important;
                    transition: all 0.3s ease !important;
                }
                
                .intelligent-presentation-highlight::before {
                    content: '' !important;
                    position: absolute !important;
                    top: -2px !important;
                    left: -2px !important;
                    right: -2px !important;
                    bottom: -2px !important;
                    background: var(--highlight-color, #ff0000) !important;
                    opacity: 0.3 !important;
                    border-radius: 4px !important;
                    pointer-events: none !important;
                    z-index: -1 !important;
                    animation: intelligent-presentation-pulse 2s infinite !important;
                }
                
                @keyframes intelligent-presentation-pulse {
                    0%, 100% { opacity: 0.3; transform: scale(1); }
                    50% { opacity: 0.6; transform: scale(1.02); }
                }
                
                .intelligent-presentation-click-indicator {
                    position: absolute !important;
                    width: 20px !important;
                    height: 20px !important;
                    border: 2px solid #ff0000 !important;
                    border-radius: 50% !important;
                    background: rgba(255, 0, 0, 0.2) !important;
                    pointer-events: none !important;
                    z-index: 10000 !important;
                    animation: intelligent-presentation-click-ripple 0.6s ease-out !important;
                }
                
                @keyframes intelligent-presentation-click-ripple {
                    0% { transform: scale(0); opacity: 1; }
                    100% { transform: scale(3); opacity: 0; }
                }
            `;
            document.head.appendChild(this.styleElement);
        },
        
        addHighlight(element, color = '#ff0000', duration = 0) {
            const id = 'highlight_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            
            element.classList.add('intelligent-presentation-highlight');
            element.style.setProperty('--highlight-color', color);
            
            this.highlights.set(id, {
                element,
                color,
                timeout: duration > 0 ? setTimeout(() => {
                    this.removeHighlight(id);
                }, duration) : null
            });
            
            return id;
        },
        
        removeHighlight(id) {
            const highlight = this.highlights.get(id);
            if (highlight) {
                highlight.element.classList.remove('intelligent-presentation-highlight');
                highlight.element.style.removeProperty('--highlight-color');
                
                if (highlight.timeout) {
                    clearTimeout(highlight.timeout);
                }
                
                this.highlights.delete(id);
                return true;
            }
            return false;
        },
        
        removeAllHighlights() {
            for (const [id] of this.highlights) {
                this.removeHighlight(id);
            }
        }
    };
    
    // 操作执行器
    const actionExecutor = {
        
        /**
         * 高亮元素
         */
        highlight(params) {
            const { selector, text, color = '#ff0000', duration = 3000 } = params;
            
            const element = this.findElement(selector, text);
            if (!element) {
                throw new Error(`未找到目标元素: ${selector || text}`);
            }
            
            const highlightId = highlightManager.addHighlight(element, color, duration);
            
            // 滚动到元素
            element.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'center',
                inline: 'center'
            });
            
            return {
                success: true,
                highlightId,
                elementInfo: this.getElementInfo(element)
            };
        },
        
        /**
         * 点击元素
         */
        click(params) {
            const { selector, text, showIndicator = true } = params;
            
            const element = this.findElement(selector, text);
            if (!element) {
                throw new Error(`未找到目标元素: ${selector || text}`);
            }
            
            // 显示点击指示器
            if (showIndicator) {
                this.showClickIndicator(element);
            }
            
            // 滚动到元素
            element.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'center',
                inline: 'center'
            });
            
            // 延迟点击，确保滚动完成
            setTimeout(() => {
                element.click();
            }, 500);
            
            return {
                success: true,
                elementInfo: this.getElementInfo(element)
            };
        },
        
        /**
         * 滚动到元素
         */
        scroll(params) {
            const { selector, text, behavior = 'smooth', block = 'center' } = params;
            
            const element = this.findElement(selector, text);
            if (!element) {
                throw new Error(`未找到目标元素: ${selector || text}`);
            }
            
            element.scrollIntoView({ 
                behavior, 
                block,
                inline: 'center'
            });
            
            return {
                success: true,
                elementInfo: this.getElementInfo(element)
            };
        },
        
        /**
         * 获取页面信息
         */
        getInfo(params = {}) {
            const { includeElements = false } = params;
            
            const info = {
                url: window.location.href,
                title: document.title,
                isIframe: window !== window.top,
                viewport: {
                    width: window.innerWidth,
                    height: window.innerHeight
                },
                scroll: {
                    x: window.scrollX,
                    y: window.scrollY
                }
            };
            
            if (includeElements) {
                info.elements = this.getAllInteractiveElements();
            }
            
            return { success: true, data: info };
        },
        
        /**
         * 移除高亮
         */
        removeHighlight(params) {
            const { highlightId } = params;
            
            if (highlightId) {
                const removed = highlightManager.removeHighlight(highlightId);
                return { success: removed };
            } else {
                highlightManager.removeAllHighlights();
                return { success: true };
            }
        },
        
        /**
         * 查找元素
         */
        findElement(selector, text) {
            if (selector) {
                return document.querySelector(selector);
            }
            
            if (text) {
                // 通过文本内容查找元素
                const walker = document.createTreeWalker(
                    document.body,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );
                
                let node;
                while (node = walker.nextNode()) {
                    if (node.textContent.trim().includes(text.trim())) {
                        return node.parentElement;
                    }
                }
            }
            
            return null;
        },
        
        /**
         * 获取元素信息
         */
        getElementInfo(element) {
            const rect = element.getBoundingClientRect();
            return {
                tagName: element.tagName,
                id: element.id,
                className: element.className,
                text: element.textContent?.trim().substring(0, 100),
                position: {
                    x: rect.left,
                    y: rect.top,
                    width: rect.width,
                    height: rect.height
                }
            };
        },
        
        /**
         * 显示点击指示器
         */
        showClickIndicator(element) {
            const rect = element.getBoundingClientRect();
            const indicator = document.createElement('div');
            indicator.className = 'intelligent-presentation-click-indicator';
            indicator.style.left = (rect.left + rect.width / 2 - 10) + 'px';
            indicator.style.top = (rect.top + rect.height / 2 - 10) + 'px';
            
            document.body.appendChild(indicator);
            
            setTimeout(() => {
                indicator.remove();
            }, 600);
        },
        
        /**
         * 获取所有可交互元素
         */
        getAllInteractiveElements() {
            const selectors = [
                'a', 'button', 'input', 'select', 'textarea',
                '[onclick]', '[role="button"]', '.btn', '.button'
            ];

            const elements = [];
            selectors.forEach(selector => {
                document.querySelectorAll(selector).forEach(el => {
                    elements.push(this.getElementInfo(el));
                });
            });

            return elements;
        },

        /**
         * 获取文本内容
         */
        getText(params) {
            const { selector, text } = params;

            if (selector) {
                const element = document.querySelector(selector);
                if (element) {
                    return {
                        success: true,
                        data: {
                            text: element.textContent?.trim(),
                            html: element.innerHTML
                        }
                    };
                }
            }

            if (text) {
                const element = this.findElement(null, text);
                if (element) {
                    return {
                        success: true,
                        data: {
                            text: element.textContent?.trim(),
                            html: element.innerHTML
                        }
                    };
                }
            }

            throw new Error('未找到目标元素');
        }
    };
    
    // 初始化
    highlightManager.init();

    // 监听来自主页面的消息
    window.addEventListener('message', (event) => {
        if (event.data && event.data.type === 'EXTENSION_REQUEST') {
            console.log('📨 内容脚本收到消息:', event.data);

            try {
                const result = actionExecutor[event.data.action](event.data.params || {});

                // 发送响应
                window.postMessage({
                    type: 'EXTENSION_RESPONSE',
                    requestId: event.data.requestId,
                    success: true,
                    data: result
                }, '*');

            } catch (error) {
                console.error('❌ 执行操作失败:', error);

                window.postMessage({
                    type: 'EXTENSION_RESPONSE',
                    requestId: event.data.requestId,
                    success: false,
                    error: error.message
                }, '*');
            }
        }
    });
    
        console.log('✅ 智能讲解助手内容脚本初始化完成');
    }

    // 根据文档状态决定何时初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initExtension);
    } else {
        initExtension();
    }

})();
