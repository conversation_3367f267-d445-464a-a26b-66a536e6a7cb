// Content Script 补丁 - 用于在iframe中监听用户事件并上报
// 这个脚本会被注入到所有iframe中，包括跨域iframe

(function() {
    'use strict';
    
    // 只在iframe中执行（不在主页面执行）
    if (window.self === window.top) {
        console.log('Content Script 补丁: 在主页面中，跳过事件监听');
        return;
    }
    
    console.log('Content Script 补丁: 在iframe中启动事件监听', window.location.href);
    
    // 生成最佳选择器
    function getBestSelector(element) {
        if (!element) return '';
        
        // 优先使用ID
        if (element.id && element.id.trim()) {
            return '#' + element.id;
        }
        
        // 使用class
        if (element.className && typeof element.className === 'string' && element.className.trim()) {
            const classes = element.className.split(' ').filter(c => c.trim());
            if (classes.length > 0) {
                return '.' + classes[0];
            }
        }
        
        // 使用tagName
        if (element.tagName) {
            return element.tagName.toLowerCase();
        }
        
        return '';
    }
    
    // 获取元素的文本内容（用于生成更精确的选择器）
    function getElementText(element) {
        if (!element) return '';
        
        // 获取可见文本
        let text = element.textContent || element.innerText || '';
        text = text.trim();
        
        // 如果是input，获取value
        if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
            text = element.value || text;
        }
        
        return text.substring(0, 50); // 限制长度
    }
    
    // 生成XPath选择器（备用方案）
    function getXPath(element) {
        if (!element) return '';
        
        if (element.id) {
            return `//*[@id="${element.id}"]`;
        }
        
        if (element === document.body) {
            return '/html/body';
        }
        
        let path = '';
        while (element && element.nodeType === Node.ELEMENT_NODE) {
            let index = 1;
            let sibling = element.previousSibling;
            
            while (sibling) {
                if (sibling.nodeType === Node.ELEMENT_NODE && sibling.tagName === element.tagName) {
                    index++;
                }
                sibling = sibling.previousSibling;
            }
            
            const tagName = element.tagName.toLowerCase();
            path = '/' + tagName + '[' + index + ']' + path;
            element = element.parentNode;
        }
        
        return path;
    }
    
    // 监听点击事件
    function handleClick(event) {
        const target = event.target;
        const selector = getBestSelector(target);
        const xpath = getXPath(target);
        const text = getElementText(target);
        
        const eventData = {
            type: 'CROSS_ORIGIN_USER_EVENT',
            eventType: 'click',
            selector: selector,
            xpath: xpath,
            text: text,
            tagName: target.tagName ? target.tagName.toLowerCase() : '',
            frameUrl: window.location.href,
            timestamp: Date.now(),
            coordinates: {
                x: event.clientX,
                y: event.clientY
            }
        };
        
        console.log('Content Script 补丁: 捕获点击事件', eventData);
        
        // 发送到父页面
        try {
            window.parent.postMessage(eventData, '*');
        } catch (error) {
            console.error('Content Script 补丁: 发送消息失败', error);
        }
    }
    
    // 监听输入事件
    function handleInput(event) {
        const target = event.target;
        
        // 只处理input、textarea、select元素
        if (!['INPUT', 'TEXTAREA', 'SELECT'].includes(target.tagName)) {
            return;
        }
        
        const selector = getBestSelector(target);
        const xpath = getXPath(target);
        const text = getElementText(target);
        
        const eventData = {
            type: 'CROSS_ORIGIN_USER_EVENT',
            eventType: 'input',
            selector: selector,
            xpath: xpath,
            text: text,
            tagName: target.tagName ? target.tagName.toLowerCase() : '',
            inputType: target.type || '',
            frameUrl: window.location.href,
            timestamp: Date.now()
        };
        
        console.log('Content Script 补丁: 捕获输入事件', eventData);
        
        // 发送到父页面
        try {
            window.parent.postMessage(eventData, '*');
        } catch (error) {
            console.error('Content Script 补丁: 发送消息失败', error);
        }
    }
    
    // 监听滚动事件（节流处理）
    let scrollTimeout;
    function handleScroll(event) {
        if (scrollTimeout) {
            clearTimeout(scrollTimeout);
        }
        
        scrollTimeout = setTimeout(() => {
            const eventData = {
                type: 'CROSS_ORIGIN_USER_EVENT',
                eventType: 'scroll',
                frameUrl: window.location.href,
                timestamp: Date.now(),
                scrollPosition: {
                    scrollX: window.scrollX,
                    scrollY: window.scrollY
                }
            };
            
            console.log('Content Script 补丁: 捕获滚动事件', eventData);
            
            try {
                window.parent.postMessage(eventData, '*');
            } catch (error) {
                console.error('Content Script 补丁: 发送消息失败', error);
            }
        }, 100); // 100ms节流
    }
    
    // 监听键盘事件
    function handleKeydown(event) {
        // 只处理特殊键（Enter、Tab、Escape等）
        const specialKeys = ['Enter', 'Tab', 'Escape', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'];
        
        if (!specialKeys.includes(event.key)) {
            return;
        }
        
        const target = event.target;
        const selector = getBestSelector(target);
        const xpath = getXPath(target);
        
        const eventData = {
            type: 'CROSS_ORIGIN_USER_EVENT',
            eventType: 'keydown',
            key: event.key,
            selector: selector,
            xpath: xpath,
            tagName: target.tagName ? target.tagName.toLowerCase() : '',
            frameUrl: window.location.href,
            timestamp: Date.now()
        };
        
        console.log('Content Script 补丁: 捕获键盘事件', eventData);
        
        try {
            window.parent.postMessage(eventData, '*');
        } catch (error) {
            console.error('Content Script 补丁: 发送消息失败', error);
        }
    }
    
    // 添加事件监听器
    document.addEventListener('click', handleClick, true);
    document.addEventListener('input', handleInput, true);
    document.addEventListener('scroll', handleScroll, true);
    document.addEventListener('keydown', handleKeydown, true);
    
    // 监听页面变化（动态加载的内容）
    const observer = new MutationObserver(function(mutations) {
        // 当DOM发生变化时，重新绑定事件（如果需要）
        console.log('Content Script 补丁: DOM发生变化，重新检查事件绑定');
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
    console.log('Content Script 补丁: 事件监听器已设置完成');
    
    // 发送就绪消息
    setTimeout(() => {
        try {
            window.parent.postMessage({
                type: 'CROSS_ORIGIN_RECORDER_READY',
                frameUrl: window.location.href,
                timestamp: Date.now()
            }, '*');
        } catch (error) {
            console.error('Content Script 补丁: 发送就绪消息失败', error);
        }
    }, 1000);
    
})(); 