/**
 * 主控制器 - 管理iframe页面加载和通信
 */
class MainController {
    constructor() {
        // 初始化属性 - 兼容不同版本的iframe id
        this.contentFrame = document.getElementById('content-frame') || document.getElementById('targetSystem');
        this.loadingOverlay = document.getElementById('loading-overlay');
        this.errorOverlay = document.getElementById('error-overlay');
        this.currentPage = 'index.html'; // 默认页面
        this.isInitialized = false;
        this.voiceControlEnabled = false;
        this.presentationActive = false;
        
        // 绑定方法
        this.handleFrameLoad = this.handleFrameLoad.bind(this);
        this.handleFrameError = this.handleFrameError.bind(this);
        this.handleMessage = this.handleMessage.bind(this);
        
        // 初始化
        this.init();
    }
    
    /**
     * 初始化控制器
     */
    init() {
        console.log('🚀 主控制器初始化...');

        // 检查iframe是否存在
        if (!this.contentFrame) {
            console.warn('⚠️ 未找到iframe元素，跳过主控制器初始化');
            return;
        }

        // 添加事件监听
        this.contentFrame.addEventListener('load', this.handleFrameLoad);
        this.contentFrame.addEventListener('error', this.handleFrameError);
        window.addEventListener('message', this.handleMessage);
        
        // 监听浏览器历史变化
        window.addEventListener('popstate', (event) => {
            if (event.state && event.state.page) {
                this.navigateTo(event.state.page, false);
            }
        });
        
        // 初始化历史记录
        if (!history.state) {
            history.replaceState({ page: this.currentPage }, '', `?page=${this.currentPage}`);
        }
        
        // 检查URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const pageParam = urlParams.get('page');
        const urlParam = urlParams.get('url');
        const baseUrlParam = urlParams.get('baseUrl');

        if (baseUrlParam) {
            // 如果有baseUrl参数，导航到该URL
            this.navigateToUrl(decodeURIComponent(baseUrlParam));
        } else if (urlParam) {
            // 如果有url参数，导航到该URL
            this.navigateToUrl(decodeURIComponent(urlParam));
        } else if (pageParam === 'home') {
            // 如果是home参数，导航到baseUrl
            if (window.API_CONFIG && window.API_CONFIG.baseUrl) {
                this.navigateToUrl(window.API_CONFIG.baseUrl);
            } else {
                this.navigateTo('index.html', false);
            }
        } else if (pageParam && (pageParam === 'index.html' || pageParam === 'realtime.html')) {
            // 如果有page参数，导航到该页面
            this.navigateTo(pageParam, false);
        }
    }
    
    /**
     * 处理iframe加载完成事件
     */
    handleFrameLoad() {
        if (!this.contentFrame) return;

        console.log('✅ 内容页面加载完成:', this.contentFrame.src);

        // 隐藏加载指示器和错误提示
        this.hideLoading();
        if (this.errorOverlay) {
            this.errorOverlay.style.display = 'none';
        }

        // 更新当前页面
        const url = new URL(this.contentFrame.src);
        const path = url.pathname.split('/').pop();
        this.currentPage = path;

        // 更新页面标题
        const title = path === 'index.html' ? '点位分布' : '青稞实况';
        document.title = `青稞种子数字化智慧化系统 - ${title}`;

        // 初始化iframe通信
        this.initFrameCommunication();

        // 标记初始化完成
        if (!this.isInitialized) {
            this.isInitialized = true;
            console.log('🎉 主控制器初始化完成');
        }
    }
    
    /**
     * 处理iframe加载错误
     */
    handleFrameError() {
        console.error('❌ 内容页面加载失败:', this.currentPage);
        this.hideLoading();
        this.errorOverlay.style.display = 'flex';

        // 更新错误信息
        const errorContent = this.errorOverlay.querySelector('.error-content p');
        if (errorContent) {
            errorContent.textContent = `无法加载页面 "${this.currentPage}"，请检查网络连接或稍后重试。`;
        }
    }
    
    /**
     * 初始化与iframe的通信
     */
    initFrameCommunication() {
        if (!this.contentFrame) return;

        try {
            // 向iframe发送初始化消息
            this.contentFrame.contentWindow.postMessage({
                type: 'MAIN_INIT',
                data: {
                    parentOrigin: window.location.origin,
                    currentPage: this.currentPage
                }
            }, '*');
            
            console.log('📡 向子页面发送初始化消息');
        } catch (error) {
            console.error('❌ 初始化通信失败:', error);
        }
    }
    
    /**
     * 处理来自iframe的消息
     */
    handleMessage(event) {
        // 安全检查
        if (!event.data || !event.data.type) return;
        
        console.log('📨 收到消息:', event.data.type);
        
        switch (event.data.type) {
            case 'NAVIGATE':
                // 处理导航请求 - 支持两种数据格式
                const targetPage = event.data.data?.page || event.data.page;
                if (targetPage) {
                    this.navigateTo(targetPage);
                }
                break;
                
            case 'VOICE_CONTROL_STATUS':
                // 更新语音控制状态
                this.voiceControlEnabled = event.data.enabled;
                break;
                
            case 'PRESENTATION_STATUS':
                // 更新讲解状态
                this.presentationActive = event.data.active;
                break;

            case 'IFRAME_READY':
                // iframe准备就绪
                console.log('✅ iframe页面准备就绪');
                break;

            case 'PAGE_LOADED':
                // 页面加载完成
                console.log('✅ iframe页面加载完成');
                break;

            case 'SHORTCUT_KEY':
                // 处理来自iframe的快捷键
                this.handleShortcutKey(event.data.data || event.data);
                break;

            case 'NAVIGATE_TO_BASE_URL':
                // 处理导航到baseUrl的请求
                this.handleNavigateToBaseUrl(event.data);
                break;

            default:
                console.log('⚠️ 未处理的消息类型:', event.data.type);
        }
    }
    
    /**
     * 导航到指定页面
     */
    navigateTo(page, updateHistory = true) {
        if (!page || (page !== 'index.html' && page !== 'realtime.html')) {
            console.error('❌ 无效的页面:', page);
            return;
        }

        if (this.currentPage === page) {
            console.log('⚠️ 已经在当前页面:', page);
            return;
        }

        console.log('🔄 导航到页面:', page);

        // 显示加载指示器
        this.showLoading(`正在加载${page === 'index.html' ? '点位分布' : '青稞实况'}...`);

        // 更新iframe src
        if (this.contentFrame) {
            this.contentFrame.src = page;
            this.currentPage = page;
        }

        // 更新URL和历史记录
        if (updateHistory) {
            const title = page === 'index.html' ? '点位分布' : '青稞实况';
            history.pushState({ page }, title, `?page=${page}`);
            document.title = `青稞种子数字化智慧化系统 - ${title}`;
        }

        // 通知iframe当前页面变化
        setTimeout(() => {
            this.notifyPageChange(page);
        }, 100);
    }
    
    /**
     * 重新加载当前页面
     */
    reload() {
        if (!this.contentFrame) return;

        this.showLoading('正在重新加载...');
        if (this.errorOverlay) {
            this.errorOverlay.style.display = 'none';
        }
        this.contentFrame.src = this.currentPage;
    }

    /**
     * 显示加载指示器
     */
    showLoading(message = '正在加载...') {
        const loadingText = this.loadingOverlay.querySelector('.loading-text');
        if (loadingText) {
            loadingText.textContent = message;
        }

        // 添加iframe加载状态
        if (this.contentFrame) {
            this.contentFrame.classList.add('loading');
        }
        if (this.loadingOverlay) {
            this.loadingOverlay.classList.remove('hidden');
        }
    }

    /**
     * 隐藏加载指示器
     */
    hideLoading() {
        // 移除iframe加载状态
        if (this.contentFrame) {
            this.contentFrame.classList.remove('loading');
        }

        // 延迟隐藏加载指示器，让用户看到加载完成的反馈
        setTimeout(() => {
            if (this.loadingOverlay) {
                this.loadingOverlay.classList.add('hidden');
            }
        }, 300);
    }

    /**
     * 通知iframe页面变化
     */
    notifyPageChange(page) {
        if (!this.contentFrame) return;

        try {
            this.contentFrame.contentWindow.postMessage({
                type: 'PAGE_CHANGED',
                data: { page, timestamp: Date.now() }
            }, '*');
        } catch (error) {
            console.warn('⚠️ 通知页面变化失败:', error);
        }
    }

    /**
     * 处理来自iframe的快捷键
     */
    handleShortcutKey(data) {
        console.log('🎹 主页面收到快捷键:', data);
        console.log('🎹 快捷键数据结构:', JSON.stringify(data));

        const keyData = data.data || data;
        console.log('🎹 处理的快捷键:', keyData.key);

        switch (keyData.key) {
            case 'presentation_toggle':
                // 启动/停止智能讲解
                if (window.PresentationSystem) {
                    if (window.presentationController && window.presentationController.isActive) {
                        window.PresentationSystem.stop();
                        console.log('🎹 快捷键: 停止智能讲解');
                    } else {
                        window.PresentationSystem.start();
                        console.log('🎹 快捷键: 启动智能讲解');
                    }
                }
                break;

            case 'presentation_pause_resume':
                // 暂停/恢复讲解
                if (window.presentationController && window.presentationController.isActive) {
                    if (window.presentationController.isPaused) {
                        window.presentationController.resume();
                        console.log('🎹 快捷键: 恢复讲解');
                    } else {
                        window.presentationController.pause();
                        console.log('🎹 快捷键: 暂停讲解');
                    }
                }
                break;

            case 'voice_toggle':
                // 启用/禁用语音控制
                if (window.voiceManager) {
                    if (window.voiceManager.isEnabled) {
                        window.voiceManager.disable();
                        console.log('🎹 快捷键: 禁用语音控制');
                    } else {
                        window.voiceManager.enable();
                        console.log('🎹 快捷键: 启用语音控制');
                    }
                }
                break;

            default:
                console.log('⚠️ 未知的快捷键:', keyData.key);
        }
    }

    /**
     * 处理导航到baseUrl的请求
     */
    handleNavigateToBaseUrl(data) {
        console.log('🏠 收到导航到baseUrl请求:', data);

        if (data.data && data.data.mode === 'include') {
            // 包含模式：在iframe中加载baseUrl
            console.log('🏠 包含模式：在iframe中加载baseUrl');
            this.navigateToUrl(data.data.url, true); // 标记为首页导航
        } else if (data.data && data.data.mode === 'direct') {
            // 直接模式：完全跳转到外部URL
            console.log('🏠 直接模式：完全跳转到外部URL');
            window.location.href = data.data.url;
        } else {
            // 不包含模式：直接跳转
            console.log('🏠 不包含模式：直接跳转到baseUrl');
            const url = data.data ? data.data.url : data.url;
            if (url) {
                window.location.href = url;
            } else {
                console.error('❌ 无效的URL:', data);
            }
        }
    }

    /**
     * 导航到指定URL（支持外部URL）
     */
    navigateToUrl(url, isHomeNavigation = false) {
        console.log('🔄 导航到URL:', url);

        // 检查是否是baseUrl或首页导航
        const isBaseUrl = (window.API_CONFIG && url === window.API_CONFIG.baseUrl) || isHomeNavigation;

        // 显示加载指示器
        const loadingText = isBaseUrl ? '正在加载首页...' : '正在加载外部页面...';
        this.showLoading(loadingText);

        // 更新iframe src
        if (this.contentFrame) {
            this.contentFrame.src = url;
            this.currentPage = url;
        }

        // 更新URL和历史记录 - 对baseUrl使用特殊处理
        if (isBaseUrl) {
            const title = '首页';
            history.pushState({ page: 'home' }, title, `?page=home`);
            document.title = `青稞种子数字化智慧化系统 - ${title}`;
        } else {
            const title = '外部页面';
            history.pushState({ url }, title, `?url=${encodeURIComponent(url)}`);
            document.title = `青稞种子数字化智慧化系统 - ${title}`;
        }

        // 通知iframe当前页面变化
        setTimeout(() => {
            this.notifyPageChange(url);
        }, 100);
    }
}

/**
 * 重新加载页面
 */
function retryLoad() {
    window.mainController.reload();
}

// 初始化主控制器
window.mainController = new MainController();

/**
 * AI系统集成管理器
 */
class AISystemIntegration {
    constructor(mainController) {
        this.mainController = mainController;
        this.voiceController = null;
        this.voiceManager = null;
        this.presentationSystem = null;
        this.isInitialized = false;

        this.init();
    }

    async init() {
        try {
            // 等待主控制器初始化完成
            await this.waitForMainController();

            // 初始化AI系统
            await this.initAISystems();

            console.log('✅ AI系统集成完成');
        } catch (error) {
            console.error('❌ AI系统集成失败:', error);
        }
    }

    async waitForMainController() {
        return new Promise((resolve) => {
            const check = () => {
                if (this.mainController.isInitialized) {
                    resolve();
                } else {
                    setTimeout(check, 100);
                }
            };
            check();
        });
    }

    async initAISystems() {
        console.log('🤖 初始化AI系统...');

        // 等待所有AI脚本加载完成
        await this.waitForAIScripts();

        // 初始化智能讲解系统
        await this.initPresentationSystem();

        // 初始化语音控制系统
        await this.initVoiceSystem();

        this.isInitialized = true;
    }

    async waitForAIScripts() {
        return new Promise((resolve) => {
            const check = () => {
                if (window.PresentationSystem && window.VoiceController && window.VoiceManager) {
                    resolve();
                } else {
                    setTimeout(check, 100);
                }
            };
            check();
        });
    }

    async initPresentationSystem() {
        try {
            if (window.PresentationSystem) {
                console.log('🎯 初始化智能讲解系统...');
                // 智能讲解系统已经在presentation-main.js中自动初始化
                this.presentationSystem = window.PresentationSystem;
            }
        } catch (error) {
            console.error('❌ 智能讲解系统初始化失败:', error);
        }
    }

    async initVoiceSystem() {
        try {
            if (window.VoiceManager) {
                console.log('🎤 初始化语音控制系统...');
                // 语音系统已经在voice-manager.js中自动初始化
                this.voiceManager = window.voiceManager;
                this.voiceController = window.voiceController;
            }
        } catch (error) {
            console.error('❌ 语音控制系统初始化失败:', error);
        }
    }

    /**
     * 向iframe发送语音命令
     */
    sendVoiceCommandToIframe(command) {
        try {
            this.mainController.contentFrame.contentWindow.postMessage({
                type: 'VOICE_COMMAND',
                command: command
            }, '*');
        } catch (error) {
            console.warn('⚠️ 发送语音命令到iframe失败:', error);
        }
    }

    /**
     * 向iframe发送讲解命令
     */
    sendPresentationCommandToIframe(command) {
        try {
            this.mainController.contentFrame.contentWindow.postMessage({
                type: 'PRESENTATION_COMMAND',
                command: command
            }, '*');
        } catch (error) {
            console.warn('⚠️ 发送讲解命令到iframe失败:', error);
        }
    }
}

// 初始化AI系统集成
window.aiSystemIntegration = new AISystemIntegration(window.mainController);
