# 🎯 智能讲解脚本完整指南

## 📖 概述

智能讲解系统是一个强大的自动化演示工具，支持元素高亮、点击操作、页面导航、动态文本获取等功能。本指南将详细介绍所有功能和参数配置。

## 📑 目录

### 基础配置
- [📋 基础结构](#-基础结构)
- [🎯 必需参数](#-必需参数)
- [⏱️ 时间控制参数](#️-时间控制参数)

### 动态文本功能
- [🔄 动态文本获取详解](#-动态文本获取详解)
- [🌟 动态文本获取示例](#-动态文本获取示例)
- [🔧 支持的计算值类型](#-支持的计算值类型)

### 高级功能
- [🎨 高级选项参数](#-高级选项参数)
- [🗣️ 语音控制参数](#️-语音控制参数)
- [🎭 条件执行](#-条件执行)

### 操作示例
- [💡 完整示例](#-完整示例)
- [🎨 样式自定义](#-样式自定义)
- [🎯 选择器技巧](#-选择器技巧)

### 执行策略
- [🚀 执行策略与最佳实践](#-执行策略与最佳实践)
- [🔄 跨域执行策略](#-跨域执行策略)
- [🎨 元素定位策略](#-元素定位策略)
- [📊 性能优化建议](#-性能优化建议)
- [🔧 错误处理与容错机制](#-错误处理与容错机制)
- [🎭 实际支持的功能](#-实际支持的功能)

### 调试与维护
- [🐛 调试技巧](#-调试技巧)
- [⚠️ 注意事项](#️-注意事项)
- [📚 扩展阅读](#-扩展阅读)

## 📋 基础结构

每个讲解步骤都是一个对象，包含以下基础参数：

```javascript
{
    id: 1,                    // 步骤ID，必须唯一
    action: 'highlight',      // 操作类型
    target: '.selector',      // 目标元素
    text: '讲解文本',         // 显示的文字内容（支持动态获取）
    duration: 3000,          // 持续时间（毫秒）
    dynamicText: {           // 动态文本配置（可选）
        source: 'element',
        selector: '.title',
        property: 'textContent',
        template: '当前标题：{content}',
        fallback: '无法获取标题'
    }
}
```

## 🎯 必需参数

### `id` (Number)
- **作用**: 步骤的唯一标识符
- **示例**: `id: 1`
- **注意**: 必须唯一，建议按顺序递增

### `action` (String)
- **作用**: 定义要执行的操作类型
- **可选值**:
  - `'highlight'` - 高亮显示元素
  - `'click'` - 点击元素
  - `'navigate'` - 页面导航
  - `'wait'` - 等待元素出现
  - `'scroll'` - 滚动到元素
  - `'input'` - 输入文本

### `target` (String | Object)
- **作用**: 指定目标元素
- **格式1 - 简单选择器**:
  ```javascript
  target: '#map'                    // ID选择器
  target: '.project-intro-panel'    // 类选择器
  target: 'button[data-type="气象监测站"]'  // 属性选择器
  ```

- **格式2 - 复杂对象**:
  ```javascript
  target: {
      selector: '#leaflet-tooltip-208',     // CSS选择器
      xpath: '/html/body/div[2]/div/div[7]', // XPath路径
      text: '墨竹工卡县',                   // 文本内容匹配
      fallbackTarget: '#map'               // 备用目标
  }
  ```

### `text` (String)
- **作用**: 讲解时显示的文字内容（静态文本，作为兜底显示）
- **示例**: `text: '欢迎来到青稞种子数字化智慧化布局图系统！'`
- **注意**: 当配置了 `dynamicText` 时，此字段作为兜底文本使用

### `dynamicText` (Object) - 🆕 动态文本获取
- **作用**: 从页面元素动态获取文本内容
- **优势**: 实时获取页面状态，内容更准确
- **基础结构**:
  ```javascript
  dynamicText: {
      source: 'element',              // 获取方式
      selector: '.target-element',    // 目标元素选择器
      property: 'textContent',        // 获取属性
      template: '当前内容：{content}', // 文本模板
      fallback: '无法获取内容'        // 兜底文本
  }
  ```

## ⏱️ 时间控制参数

### `duration` (Number)
- **作用**: 当前步骤的持续时间（毫秒）
- **示例**: `duration: 3000` (3秒)
- **默认值**: 3000毫秒

### `timeout` (Number)
- **作用**: 等待操作的超时时间（毫秒）
- **适用于**: `wait` 操作
- **示例**: `timeout: 5000` (5秒)

## 🔄 动态文本获取详解

动态文本获取功能允许您从页面元素实时获取内容，使讲解更加准确和动态。

### 📝 `dynamicText` 参数详解

#### `source` (String) - 获取方式
- **`'element'`**: 获取元素的文本内容或属性
- **`'attribute'`**: 获取元素的特定属性值
- **`'computed'`**: 获取计算值（如元素数量、当前时间等）
- **`'multiple'`**: 组合多个元素的内容

#### `selector` (String) - 目标元素选择器
- 支持所有CSS选择器语法
- 支持跨域iframe中的元素
- 示例: `'.title'`, `'#map'`, `'button[data-type="weather"]'`

#### `property` (String) - 获取属性
- **文本内容**: `'textContent'`, `'innerText'`, `'innerHTML'`
- **表单值**: `'value'`
- **HTML属性**: `'id'`, `'class'`, `'src'`, `'href'` 等
- **自定义属性**: `'data-*'` 属性

#### `template` (String) - 文本模板
- 使用 `{content}` 作为占位符
- 示例: `'当前标题是：{content}'`
- 多元素时使用 `{0}`, `{1}`, `{2}` 等

#### `fallback` (String) - 兜底文本
- 当动态获取失败时显示的文本
- 建议提供有意义的兜底内容

### 🌟 动态文本获取示例

#### 1. 基础元素文本获取
```javascript
{
    id: 1,
    action: 'highlight',
    target: '.page-title',
    text: '页面标题获取示例',
    dynamicText: {
        source: 'element',
        selector: '.page-title h1',
        property: 'textContent',
        template: '当前页面标题：{content}',
        fallback: '无法获取页面标题'
    },
    duration: 3000
}
```

#### 2. 获取元素属性
```javascript
{
    id: 2,
    action: 'highlight',
    target: '#map',
    text: '地图容器信息',
    dynamicText: {
        source: 'attribute',
        selector: '#map',
        property: 'id',
        template: '地图容器ID：{content}',
        fallback: '无法获取地图信息'
    },
    duration: 3000
}
```

#### 3. 计算值获取
```javascript
{
    id: 3,
    action: 'wait',
    target: '.device-list',
    text: '设备统计信息',
    dynamicText: {
        source: 'computed',
        selector: '.device-item',
        property: 'count',
        template: '当前共有{content}个监测设备',
        fallback: '无法统计设备数量'
    },
    duration: 3000
}
```

#### 4. 多元素组合
```javascript
{
    id: 4,
    action: 'highlight',
    target: '.navigation',
    text: '导航信息组合',
    dynamicText: {
        source: 'multiple',
        selectors: [
            { selector: '.nav-item:first-child', property: 'textContent' },
            { selector: '.nav-item:last-child', property: 'textContent' }
        ],
        template: '导航包含：{0} 和 {1} 两个功能',
        fallback: '无法获取导航信息'
    },
    duration: 4000
}
```

#### 5. 跨域iframe元素获取
```javascript
{
    id: 5,
    action: 'highlight',
    target: 'iframe',
    text: 'iframe中的元素',
    dynamicText: {
        source: 'element',
        selector: '.layer-control-panel label:first-child',
        property: 'textContent',
        template: '第一个图层选项：{content}',
        fallback: '无法获取图层信息'
    },
    duration: 3000
}
```

### 🔧 支持的计算值类型

#### 基础计算值
- **`'count'`**: 统计匹配元素的数量
- **`'visible'`**: 检查元素是否可见
- **`'timestamp'`**: 获取当前时间
- **`'url'`**: 获取当前页面地址
- **`'domain'`**: 获取当前域名

#### 计算值示例
```javascript
// 统计在线设备数量
dynamicText: {
    source: 'computed',
    selector: '.device[data-status="online"]',
    property: 'count',
    template: '在线设备：{content}台'
}

// 获取当前时间
dynamicText: {
    source: 'computed',
    selector: 'body',
    property: 'timestamp',
    template: '当前时间：{content}'
}

// 检查元素可见性
dynamicText: {
    source: 'computed',
    selector: '.popup-modal',
    property: 'visible',
    template: '弹窗状态：{content}'
}
```

## 🎵 语音控制参数

### `speechOptions` (Object)
- **作用**: 控制语音播放的参数
- **可选属性**:
  ```javascript
  speechOptions: {
      rate: 0.8,      // 语速 (0.1-10, 默认1)
      pitch: 1.2,     // 音调 (0-2, 默认1)
      volume: 0.9,    // 音量 (0-1, 默认1)
      voice: 'Microsoft Yaoyao'  // 指定语音
  }
  ```

### `speech` (Boolean)
- **作用**: 是否播放语音
- **示例**: `speech: false` (禁用语音)
- **默认值**: true

## 🔧 操作选项参数

### `options` (Object)
- **作用**: 为特定操作提供额外配置
- **常用属性**:

#### 等待相关
```javascript
options: {
    waitFor: '.device-legend-panel',  // 等待特定元素出现
    timeout: 8000                     // 等待超时时间
}
```

#### 点击相关
```javascript
options: {
    doubleClick: true,               // 双击而非单击
    waitAfterClick: 1000,           // 点击后等待时间
    preventDefault: true             // 阻止默认行为
}
```

#### 滚动相关
```javascript
options: {
    behavior: 'smooth',             // 滚动行为
    block: 'center',               // 垂直对齐
    inline: 'nearest'              // 水平对齐
}
```

## 🎯 特殊参数

### `fallbackTarget` (String)
- **作用**: 当主目标找不到时的备用目标
- **示例**: `fallbackTarget: '#map'`
- **使用场景**: 动态生成的元素或不稳定的选择器

### `value` (String)
- **作用**: 用于 `input` 操作的输入值
- **示例**: `value: '搜索关键词'`

### `url` (String)
- **作用**: 用于 `navigate` 操作的目标URL
- **示例**: `url: 'realtime.html'`

## 📝 完整示例

```javascript
{
    id: 4,
    action: 'click',
    target: {
        text: '墨竹工卡县',
        fallbackTarget: '#map'
    },
    text: '现在我们点击墨竹工卡县，进入该地区的详细监测页面。',
    duration: 2000,
    speechOptions: {
        rate: 0.8,
        pitch: 1.1
    },
    options: {
        waitFor: '#device-legend-panel',
        timeout: 8000
    }
}
```

## 🔍 查看参数使用方法

### 1. 查看源码文件
- **脚本定义**: `AI/presentation-script.js`
- **控制器实现**: `AI/presentation-controller.js`
- **UI控制**: `AI/ui-controller.js`

### 2. 控制台调试
```javascript
// 查看当前脚本
console.log(window.presentationScript);

// 查看控制器状态
console.log(window.presentationController);
```

### 3. 实时修改测试
```javascript
// 修改当前步骤
window.presentationScript[0].duration = 5000;

// 添加新步骤
window.presentationScript.push({
    id: 999,
    action: 'highlight',
    target: '.new-element',
    text: '测试新步骤',
    duration: 3000
});
```

## 🚀 高级用法示例

### 1. 复杂点击操作
```javascript
{
    id: 10,
    action: 'click',
    target: {
        selector: 'button[data-device-id="12345"]',
        text: '气象监测站',
        fallbackTarget: '.device-legend-panel .legend-item:first-child'
    },
    text: '点击气象监测站查看详细信息',
    duration: 2000,
    options: {
        waitFor: '.device-popup',
        timeout: 5000,
        doubleClick: false,
        waitAfterClick: 1000
    }
}
```

### 2. 页面导航
```javascript
{
    id: 15,
    action: 'navigate',
    target: 'realtime.html',
    text: '正在跳转到实时监控页面...',
    duration: 3000,
    options: {
        waitFor: '.realtime-container',
        timeout: 10000
    }
}
```

### 3. 文本输入
```javascript
{
    id: 20,
    action: 'input',
    target: '#search-input',
    value: '墨竹工卡县',
    text: '在搜索框中输入县名进行搜索',
    duration: 2000,
    options: {
        clearFirst: true,      // 先清空输入框
        typeSpeed: 100,        // 打字速度（毫秒/字符）
        submitAfter: true      // 输入后按回车
    }
}
```

### 4. 等待操作
```javascript
{
    id: 25,
    action: 'wait',
    target: '.loading-spinner',
    text: '正在加载数据，请稍候...',
    timeout: 8000,
    duration: 1000,
    options: {
        waitForDisappear: true  // 等待元素消失而非出现
    }
}
```

### 5. 滚动操作
```javascript
{
    id: 30,
    action: 'scroll',
    target: '.device-list',
    text: '滚动查看更多设备信息',
    duration: 2000,
    options: {
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest',
        offset: { top: -100 }   // 额外偏移量
    }
}
```

## 🎨 样式自定义

### 高亮样式配置
```javascript
{
    id: 35,
    action: 'highlight',
    target: '.special-element',
    text: '这是一个特殊的高亮效果',
    duration: 3000,
    options: {
        highlightStyle: {
            borderColor: '#ff6b6b',
            borderWidth: '4px',
            boxShadow: '0 0 30px rgba(255, 107, 107, 0.8)',
            animation: 'pulse 1.5s infinite'
        },
        overlayOpacity: 0.3
    }
}
```

## 🔄 条件执行

### 基于元素存在性的条件执行
```javascript
{
    id: 40,
    action: 'click',
    target: '.optional-button',
    text: '如果按钮存在则点击',
    duration: 2000,
    options: {
        skipIfNotFound: true,   // 找不到元素时跳过而非报错
        retryCount: 3,          // 重试次数
        retryDelay: 1000        // 重试间隔
    }
}
```

## 🎯 选择器技巧

### 1. 多重选择器
```javascript
target: '.button1, .button2, #fallback-btn'  // 任一匹配即可
```

### 2. 属性选择器
```javascript
target: 'button[data-type="weather"][data-status="online"]'
```

### 3. 文本内容选择器
```javascript
target: 'button:contains("返回主视图")'
```

### 4. 伪类选择器
```javascript
target: '.device-list .device-item:first-child'
target: '.legend-item:nth-child(2)'
target: 'input:focus'
```

## 🐛 调试技巧

### 1. 启用详细日志
```javascript
// 在控制台中启用调试模式
window.presentationController.debugMode = true;
```

### 2. 单步执行
```javascript
// 暂停自动播放
window.presentationController.pause();

// 手动执行下一步
window.presentationController.next();
```

### 3. 跳转到指定步骤
```javascript
// 跳转到第5步
window.presentationController.goToStep(5);
```

## ⚠️ 注意事项

1. **选择器稳定性**: 优先使用ID和稳定的类名，避免使用动态生成的选择器
2. **超时设置**: 为网络请求和动画设置合理的超时时间
3. **备用方案**: 为关键操作提供 `fallbackTarget`
4. **性能考虑**: 避免过长的 `duration` 和过短的 `timeout`
5. **用户体验**: 确保文本内容清晰易懂，语音播放自然流畅

## 🚀 执行策略与最佳实践

### 📋 执行策略概述

为了优化智能讲解的执行效率，避免无效尝试，我们引入了执行策略系统。开发者可以在编写流程时明确指定每个步骤的执行策略，系统会根据策略选择最优的执行方式。

### 🎯 策略类型

#### 1. **自动策略** (默认)
```javascript
options: {
    // 不指定任何策略参数，系统自动判断
}
```
- **行为**: 先尝试传统方法，失败后尝试扩展方法
- **适用**: 不确定元素位置或通用场景

#### 2. **跨域优先策略**
```javascript
options: {
    crossDomain: true  // 明确指定为跨域元素
}
```
- **行为**: 优先使用扩展方法，失败后尝试传统方法
- **适用**: 已知元素在跨域iframe中

#### 3. **本域优先策略**
```javascript
options: {
    crossDomain: false  // 明确指定为本域元素
}
```
- **行为**: 优先使用传统方法，失败后尝试扩展方法
- **适用**: 已知元素在本项目页面中

#### 4. **强制扩展策略**
```javascript
options: {
    forceExtension: true  // 强制只使用扩展方法
}
```
- **行为**: 只使用扩展方法，不尝试传统方法
- **适用**: 确定只能通过扩展访问的元素

#### 5. **强制传统策略**
```javascript
options: {
    forceTraditional: true  // 强制只使用传统方法
}
```
- **行为**: 只使用传统方法，不尝试扩展方法
- **适用**: 确定可以直接访问的元素

### 📝 实际应用示例

#### 示例1：跨域iframe中的元素
```javascript
{
    id: 20,
    action: 'highlight',
    target: {
        selector: '.col2:nth-of-type(2) img.image.image2',
        fallbackTarget: 'img.image.image2:nth-of-type(2)'
    },
    text: '高亮跨域iframe中的图片',
    duration: 3000,
    options: {
        crossDomain: true,  // 🎯 优先使用扩展方法
        skipIfNotFound: true
    }
}
```

#### 示例2：本项目页面中的元素
```javascript
{
    id: 10,
    action: 'click',
    target: '#local-button',
    text: '点击本地按钮',
    duration: 2000,
    options: {
        crossDomain: false,  // 🎯 优先使用传统方法
        skipIfNotFound: true
    }
}
```

#### 示例3：确定只能通过扩展访问
```javascript
{
    id: 30,
    action: 'highlight',
    target: '.third-party-element',
    text: '高亮第三方系统元素',
    duration: 3000,
    options: {
        forceExtension: true,  // 🎯 强制使用扩展
        skipIfNotFound: true
    }
}
```

### 🚀 性能优化效果

#### 优化前
```
📄 尝试传统方法... ❌ 失败 (浪费时间)
🔌 尝试扩展方法... ✅ 成功
总耗时: ~2-3秒
```

#### 优化后 (使用 crossDomain: true)
```
🔌 直接使用扩展方法... ✅ 成功
总耗时: ~0.5秒
```

### ⚠️ 注意事项

1. **策略优先级**：
   - `forceExtension` > `forceTraditional` > `crossDomain` > 自动判断

2. **容错处理**：
   - 即使指定了策略，系统仍会在失败时尝试备选方案（除非使用force选项）

3. **扩展可用性**：
   - 如果扩展不可用，强制扩展策略会失败
   - 建议在生产环境中确保扩展正常工作

4. **调试信息**：
   - 控制台会显示使用的策略和执行结果
   - 便于调试和优化

### 🔧 最佳实践

1. **明确标记跨域元素**：为iframe中的元素设置 `crossDomain: true`
2. **使用备选选择器**：配合 `fallbackTarget` 提高成功率
3. **合理使用强制策略**：只在确定的场景下使用force选项
4. **监控执行日志**：根据日志优化策略配置

### 📊 策略决策流程图

```
开始
  ↓
检查options中的策略参数
  ↓
forceExtension? → 是 → 只使用扩展方法
  ↓ 否
forceTraditional? → 是 → 只使用传统方法
  ↓ 否
crossDomain: true? → 是 → 扩展优先
  ↓ 否
crossDomain: false? → 是 → 传统优先
  ↓ 否
使用自动策略（传统优先）
```

### 🔄 跨域执行策略

#### 本地模式执行
- **适用**: 同域iframe和本地页面
- **优势**: 直接DOM访问，执行速度快
- **限制**: 无法访问跨域内容

#### 扩展模式执行
- **适用**: 跨域页面和第三方网站
- **优势**: 突破跨域限制，功能完整
- **要求**: 需要安装浏览器扩展

#### 混合模式执行
- **策略**: 优先本地模式，失败时自动切换扩展模式
- **实现**: 系统自动检测并选择最佳执行方式

### 🎨 元素定位策略

#### 1. 备选选择器策略（实际支持）
```javascript
target: {
    selector: '.primary-selector',
    fallbackTarget: '.backup-selector'  // 备选选择器
}
```

#### 2. 等待策略（实际支持）
```javascript
{
    action: 'wait',
    target: '.loading-element',
    text: '等待元素出现',
    timeout: 5000,                    // 等待超时时间
    options: {
        waitForDisappear: true        // 等待元素消失
    }
}
```

#### 3. 容错处理策略（实际支持）
```javascript
{
    action: 'highlight',
    target: '.may-not-exist',
    text: '尝试高亮元素',
    options: {
        skipIfNotFound: true          // 找不到时跳过
    }
}
```

### 📊 性能优化建议

#### 1. 合理设置超时时间
```javascript
{
    action: 'wait',
    target: '.slow-loading-element',
    timeout: 10000,                   // 根据实际情况调整
    text: '等待慢速元素加载'
}
```

#### 2. 使用备选选择器
```javascript
{
    action: 'click',
    target: {
        selector: '.primary-button',
        fallbackTarget: 'button[data-action="submit"]'  // 备选方案
    },
    text: '点击提交按钮'
}
```

#### 3. 明确指定执行策略
```javascript
{
    action: 'highlight',
    target: '.iframe-element',
    text: '高亮iframe中的元素',
    options: {
        crossDomain: true,            // 明确指定跨域策略
        skipIfNotFound: true          // 容错处理
    }
}
```

### 🔧 错误处理与容错机制

#### 1. 跳过不存在的元素
```javascript
{
    action: 'click',
    target: '.optional-button',
    text: '尝试点击可选按钮',
    options: {
        skipIfNotFound: true          // 找不到元素时跳过
    }
}
```

#### 2. 使用备选目标
```javascript
{
    action: 'highlight',
    target: {
        selector: '.primary-element',
        fallbackTarget: '.backup-element'  // 备选元素
    },
    text: '高亮目标元素'
}
```

#### 3. 设置合理超时
```javascript
{
    action: 'wait',
    target: '.slow-element',
    text: '等待元素加载',
    timeout: 8000,                    // 8秒超时
    options: {
        waitForDisappear: false       // 等待出现而非消失
    }
}
```

### 🎭 实际支持的功能

#### 1. 滚动操作
```javascript
{
    action: 'scroll',
    target: '.content-area',
    text: '滚动到目标区域',
    options: {
        behavior: 'smooth',           // 平滑滚动
        block: 'center',              // 垂直对齐方式
        inline: 'nearest'             // 水平对齐方式
    }
}
```

#### 2. 页面导航
```javascript
{
    action: 'navigate',
    target: 'realtime.html',
    text: '跳转到实时监控页面',
    options: {
        waitFor: '.realtime-container', // 等待特定元素出现
        timeout: 10000                  // 导航超时时间
    }
}
```

#### 3. 文本输入
```javascript
{
    action: 'input',
    target: '#search-input',
    value: '搜索内容',
    text: '在搜索框中输入内容',
    options: {
        clearFirst: true,             // 先清空输入框
        submitAfter: false            // 输入后不自动提交
    }
}
```



## 🚀 快速开始

### 1. 基础脚本示例
```javascript
// 创建一个简单的讲解脚本
window.presentationScript = [
    {
        id: 1,
        action: 'highlight',
        target: '.page-title',
        text: '欢迎来到智能讲解系统！',
        duration: 3000
    },
    {
        id: 2,
        action: 'click',
        target: '.start-button',
        text: '点击开始按钮进入系统',
        duration: 2000
    }
];

// 启动讲解
window.PresentationSystem.start();
```

### 2. 动态文本脚本示例
```javascript
// 使用动态文本获取的讲解脚本
window.presentationScript = [
    {
        id: 1,
        action: 'highlight',
        target: '.page-header',
        text: '页面标题获取示例',
        dynamicText: {
            source: 'element',
            selector: 'title',
            property: 'textContent',
            template: '当前页面：{content}',
            fallback: '无法获取页面标题'
        },
        duration: 4000
    }
];
```

### 3. 跨域脚本示例
```javascript
// 适用于iframe和第三方页面的脚本
window.presentationScript = [
    {
        id: 1,
        action: 'highlight',
        target: '.iframe-element',
        text: 'iframe中的元素高亮',
        dynamicText: {
            source: 'element',
            selector: '.content-title',
            property: 'textContent',
            template: 'iframe内容：{content}',
            fallback: '正在加载iframe内容...'
        },
        duration: 3000
    }
];
```

### 4. 控制命令
```javascript
// 启动讲解
window.PresentationSystem.start();

// 暂停讲解
window.presentationController.pause();

// 继续讲解
window.presentationController.resume();

// 停止讲解
window.presentationController.stop();

// 跳转到指定步骤
window.presentationController.goToStep(5);

// 手动执行下一步
window.presentationController.next();

// 手动执行上一步
window.presentationController.previous();
```

## 📋 常见问题

### Q: 如何处理元素找不到的情况？
A: 使用 `fallbackTarget` 或在 `options` 中设置 `skipIfNotFound: true`

### Q: 如何在第三方网站使用？
A: 安装浏览器扩展，系统会自动切换到扩展模式

### Q: 动态文本获取失败怎么办？
A: 系统会自动使用 `fallback` 文本或静态 `text` 作为兜底

### Q: 如何调试脚本执行？
A: 在控制台设置 `window.presentationController.debugMode = true`

### Q: 如何自定义高亮样式？
A: 在步骤的 `options.highlightStyle` 中设置自定义CSS样式

## 📚 扩展阅读

- 查看 `AI/presentation-controller.js` 了解操作实现细节
- 查看 `AI/speech-engine.js` 了解语音控制选项
- 查看 `AI/ui-controller.js` 了解UI交互效果
- 查看 `browser-extension/` 了解扩展模式实现
- 查看 `js/iframe-communication.js` 了解跨域通信机制
- 查看 `AI/presentation-script-dynamic-text-test.js` 了解动态文本测试示例

---

**版本**: v2.0.0
**更新日期**: 2024年
**新增功能**: 动态文本获取、跨域支持、执行策略优化
