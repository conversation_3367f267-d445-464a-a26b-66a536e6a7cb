/**
 * 语音引擎 - 负责文字转语音功能
 */
class SpeechEngine {
    constructor() {
        this.synthesis = window.speechSynthesis;
        this.currentUtterance = null;
        this.isSupported = 'speechSynthesis' in window;
        this.voices = [];
        this.isPaused = false;
        this.currentText = '';
        this.currentPosition = 0;

        // 统一的语音配置中心
        this.config = {
            // 基础语音参数
            voice: {
                name: 'Microsoft Xiaoxiao',  // 语音名称
                lang: 'zh-CN',             // 语言
                rate: 1.2,                 // 语速 (0.1-10，推荐0.5-1.5)
                pitch: 1.0,                // 音调 (0-2，推荐0.8-1.2)
                volume: 0.8                // 音量 (0-1)
            },
            // 推荐的语音配置
            presets: {
                'huihui': { name: 'Microsoft Huihui', rate: 1.0, pitch: 1.0, volume: 0.8, description: '女声，温和亲切' },
                'yaoyao': { name: 'Microsoft Yaoyao', rate: 1.0, pitch: 1.0, volume: 0.8, description: '女声，清晰自然，推荐用于讲解' },
                'kangkang': { name: 'Microsoft Kangkang', rate: 1.0, pitch: 0.9, volume: 0.8, description: '男声，稳重专业' }
            },
            // 错误处理配置
            errorHandling: {
                retryCount: 2,             // 重试次数
                fallbackToSilent: true,    // 失败时静默处理
                logErrors: true            // 是否记录错误
            }
        };

        this.init();

        // 页面卸载时停止语音
        window.addEventListener('beforeunload', () => {
            this.stop();
        });

        // 页面隐藏时停止语音
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stop();
            }
        });
    }

    /**
     * 初始化语音引擎
     */
    async init() {
        if (!this.isSupported) {
            console.warn('当前浏览器不支持语音合成功能');
            return;
        }

        // 等待语音列表加载
        await this.loadVoices();
        

    }

    /**
     * 加载可用的语音列表
     */
    async loadVoices() {
        return new Promise((resolve) => {
            let attempts = 0;
            const maxAttempts = 50; // 最多尝试5秒

            const updateVoices = () => {
                this.voices = this.synthesis.getVoices();
                //console.log(`语音列表加载尝试 ${attempts + 1}，找到 ${this.voices.length} 个语音`);

                if (this.voices.length > 0) {
                    //console.log('可用语音:', this.voices.map(v => `${v.name} (${v.lang})`));
                    //console.log(window.presentationController.speechEngine.getRecommendedVoices())
                    
                    resolve();
                } else if (attempts < maxAttempts) {
                    attempts++;
                    setTimeout(updateVoices, 100);
                } else {
                    console.warn('语音列表加载超时，使用默认语音');
                    resolve();
                }
            };

            // 监听语音列表变化
            this.synthesis.addEventListener('voiceschanged', updateVoices);
            updateVoices();
        });
    }

    /**
     * 检查语音播放权限
     */
    checkSpeechPermission() {
        // 检查是否有用户交互
        if (!this.hasUserInteraction()) {
            return false;
        }

        // 检查语音合成是否可用
        if (!this.synthesis) {
            return false;
        }

        // 检查是否被暂停或取消
        if (this.synthesis.paused || this.synthesis.pending) {
            console.log('语音合成被暂停或有待处理的任务');
            return false;
        }

        return true;
    }

    /**
     * 检查是否有用户交互
     */
    hasUserInteraction() {
        // 简单返回true，因为用户已经点击了测试按钮
        return true;
    }

    /**
     * 播放语音 - 使用简单直接的方法
     * @param {string} text - 要播放的文本
     * @param {object} options - 语音选项
     * @returns {Promise} - 播放完成的Promise
     */
    async speak(text, options = {}) {
        if (!this.isSupported) {
            console.warn('语音功能不可用，跳过语音播放');
            return Promise.resolve();
        }

        if (!text || text.trim() === '') {
            return Promise.resolve();
        }

        // 停止之前的语音并重置状态
        this.stop();

        // 等待一小段时间确保语音合成器完全停止
        await new Promise(resolve => setTimeout(resolve, 50));

        return new Promise((resolve) => {
            try {
                // 创建语音实例 - 使用简单直接的方法
                const utterance = new SpeechSynthesisUtterance(text);

                // 设置语音参数 - 使用统一配置
                utterance.rate = options.rate || this.config.voice.rate;
                utterance.pitch = options.pitch || this.config.voice.pitch;
                utterance.volume = options.volume || this.config.voice.volume;
                utterance.lang = options.lang || this.config.voice.lang;

                // 使用配置中指定的语音
                const selectedVoice = this.selectVoice(utterance.lang, this.config.voice.name);

                if (selectedVoice) {
                    utterance.voice = selectedVoice;
                }

                // 保存当前utterance引用
                this.currentUtterance = utterance;
                this.currentText = text;
                this.isPaused = false;

                // 设置事件监听
                utterance.onstart = () => {
                    // 语音开始播放
                };

                utterance.onend = () => {
                    this.currentUtterance = null;
                    this.currentText = '';
                    resolve();
                };

                utterance.onerror = (event) => {
                    // 对于interrupted错误，这通常是正常的停止操作，不需要报错
                    if (event.error !== 'interrupted') {
                        console.log(`⚠️ 语音播放错误: ${event.error}`);
                    }
                    this.currentUtterance = null;
                    this.currentText = '';
                    // 不管什么错误都resolve，避免阻塞流程
                    resolve();
                };

                // 播放语音
                window.speechSynthesis.speak(utterance);

            } catch (error) {
                console.error('语音播放异常:', error);
                resolve(); // 异常也resolve，避免阻塞
            }
        });
    }

    /**
     * 停止当前语音播放
     */
    stop() {
        try {
            // 简单直接的停止方法
            if (window.speechSynthesis) {
                window.speechSynthesis.cancel();
            }

            // 重置内部状态
            this.currentUtterance = null;
            this.isPaused = false;
            this.currentText = '';
            this.currentPosition = 0;

        } catch (error) {
            console.error('停止语音播放时出错:', error);
        }
    }

    /**
     * 暂停语音播放
     */
    pause() {
        try {
            if (this.synthesis.speaking && !this.synthesis.paused) {
                this.synthesis.pause();
                this.isPaused = true;
            }
        } catch (error) {
            if (this.config.errorHandling.logErrors) {
                console.warn('暂停语音播放时出错:', error);
            }
        }
    }

    /**
     * 恢复语音播放
     */
    resume() {
        try {
            if (this.synthesis.paused) {
                this.synthesis.resume();
                this.isPaused = false;
            }
        } catch (error) {
            if (this.config.errorHandling.logErrors) {
                console.warn('恢复语音播放时出错:', error);
            }
        }
    }

    /**
     * 选择合适的语音
     * @param {string} lang - 语言代码
     * @param {string} voiceName - 指定的语音名称
     * @returns {SpeechSynthesisVoice|null}
     */
    selectVoice(lang, voiceName) {
        if (!this.voices.length) return null;

        // 如果指定了语音名称，优先使用
        if (voiceName) {
            const namedVoice = this.voices.find(voice => 
                voice.name.includes(voiceName) || voice.name === voiceName
            );
            if (namedVoice) return namedVoice;
        }

        // 根据语言选择
        const langVoices = this.voices.filter(voice => voice.lang === lang);
        if (langVoices.length > 0) {
            // 优先选择本地语音
            const localVoice = langVoices.find(voice => voice.localService);
            return localVoice || langVoices[0];
        }

        // 模糊匹配语言（如 zh-CN 匹配 zh）
        const langPrefix = lang.split('-')[0];
        const prefixVoices = this.voices.filter(voice => 
            voice.lang.startsWith(langPrefix)
        );
        if (prefixVoices.length > 0) {
            const localVoice = prefixVoices.find(voice => voice.localService);
            return localVoice || prefixVoices[0];
        }

        // 返回默认语音
        return this.voices[0];
    }

    /**
     * 获取可用的中文语音列表
     */
    getChineseVoices() {
        return this.voices.filter(voice => 
            voice.lang.startsWith('zh') || 
            voice.name.includes('Chinese') ||
            voice.name.includes('中文')
        );
    }

    /**
     * 测试语音功能
     */
    async test(text = '这是一个语音测试') {
        try {
            console.log('开始语音测试...');
            await this.speak(text);
            console.log('语音测试完成');
            return true;
        } catch (error) {
            console.error('语音测试失败:', error);
            return false;
        }
    }

    /**
     * 获取语音引擎状态
     */
    getStatus() {
        return {
            isSupported: this.isSupported,
            isPlaying: this.synthesis.speaking,
            isPaused: this.synthesis.paused,
            voiceCount: this.voices.length,
            currentText: this.currentUtterance ? this.currentUtterance.text : null
        };
    }

    /**
     * 统一配置语音参数
     * @param {object} config - 配置对象
     */
    setConfig(config) {
        this.config = { ...this.config, ...config };
        if (this.config.errorHandling.logErrors) {
            console.log('语音配置已更新:', this.config);
        }
    }

    /**
     * 使用预设配置
     * @param {string} presetName - 预设名称 ('yaoyao', 'huihui', 'kangkang')
     */
    usePreset(presetName) {
        const preset = this.config.presets[presetName];
        if (preset) {
            this.config.voice = { ...this.config.voice, ...preset };
            if (this.config.errorHandling.logErrors) {
                console.log(`已切换到预设配置: ${presetName}`, preset);
            }
            return true;
        } else {
            if (this.config.errorHandling.logErrors) {
                console.warn(`未找到预设配置: ${presetName}`);
            }
            return false;
        }
    }

    /**
     * 快速配置语音参数（保持向后兼容）
     * @param {string} voiceName - 语音名称
     * @param {number} rate - 语速
     * @param {number} pitch - 音调
     * @param {number} volume - 音量
     */
    configure(voiceName, rate = 0.7, pitch = 1, volume = 0.8) {
        this.config.voice = {
            ...this.config.voice,
            name: voiceName,
            rate: rate,
            pitch: pitch,
            volume: volume
        };


    }

    /**
     * 应用预设配置
     * @param {string} presetName - 预设名称 ('huihui', 'yaoyao', 'kangkang')
     */
    applyPreset(presetName) {
        const preset = this.config.presets[presetName];
        if (preset) {
            this.config.voice = {
                ...this.config.voice,
                name: preset.name,
                rate: preset.rate,
                pitch: preset.pitch,
                volume: preset.volume
            };

        } else {
            console.error(`❌ 未找到预设配置: ${presetName}`);
        }
    }

    /**
     * 获取推荐的中文语音配置
     */
    getRecommendedVoices() {
        const chineseVoices = this.getChineseVoices();

        return {
            // 当前配置
            current: this.config.voice,
            // 预设配置
            presets: Object.entries(this.config.presets).map(([key, preset]) => ({
                key,
                ...preset
            })),
            // 所有可用的中文语音
            available: chineseVoices.map(voice => ({
                name: voice.name,
                lang: voice.lang,
                localService: voice.localService
            }))
        };
    }

    /**
     * 获取当前语音状态
     */
    getVoiceStatus() {
        return {
            isSupported: this.isSupported,
            isPlaying: this.synthesis.speaking,
            isPaused: this.synthesis.paused || this.isPaused,
            currentText: this.currentText,
            currentPosition: this.currentPosition,
            config: this.config.voice
        };
    }

    /**
     * 预处理文本（处理特殊字符、标点等）
     */
    preprocessText(text) {
        return text
            .replace(/\s+/g, ' ')           // 合并多个空格
            .replace(/[。！？]/g, '$&，')    // 在句号后添加停顿
            .replace(/[，；]/g, '$&，')      // 在逗号后添加停顿
            .trim();
    }

    /**
     * 分段播放长文本
     */
    async speakLongText(text, options = {}) {
        const maxLength = options.maxLength || 200;

        if (text.length <= maxLength) {
            return this.speak(text, options);
        }

        // 按句子分割
        const sentences = text.split(/[。！？]/).filter(s => s.trim());

        for (let i = 0; i < sentences.length; i++) {
            if (sentences[i].trim()) {
                await this.speak(sentences[i] + '。', options);

                // 句子间停顿
                if (i < sentences.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }
        }
    }

    /**
     * 测试语音功能
     */
    async testSpeech() {
        console.log('开始语音功能测试...');

        // 检查基本支持
        console.log('语音合成支持:', this.isSupported);
        console.log('可用语音数量:', this.voices.length);
        console.log('用户交互标志:', window.speechUserInteraction);

        if (!this.isSupported) {
            console.error('❌ 浏览器不支持语音合成');
            return false;
        }

        if (this.voices.length === 0) {
            console.warn('⚠️ 没有可用的语音，尝试重新加载...');
            await this.loadVoices();
        }

        // 设置用户交互标志
        window.speechUserInteraction = true;

        try {
            console.log('🔊 测试语音播放...');
            await this.speak('语音测试成功！智能讲解系统语音功能正常工作。', {
                rate: 0.8,
                pitch: 1,
                volume: 1
            });
            console.log('✅ 语音测试成功');
            return true;
        } catch (error) {
            console.error('❌ 语音测试失败:', error);
            return false;
        }
    }
}

// 暴露到全局作用域
window.SpeechEngine = SpeechEngine;
