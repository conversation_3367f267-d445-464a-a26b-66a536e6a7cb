/**
 * 动态文本获取功能测试脚本
 * 用于验证动态文本获取在不同场景下的工作效果
 */
window.presentationScriptDynamicTextTest = [
    // 测试1: 获取页面标题（主页面元素）
    {
        id: 1,
        action: 'wait',
        target: 'body',
        text: '这是静态文本，作为兜底显示',
        dynamicText: {
            source: 'element',
            selector: 'title',
            property: 'textContent',
            template: '欢迎使用{content}！',
            fallback: '无法获取页面标题'
        },
        duration: 4000
    },

    // 测试2: 获取iframe的src属性
    {
        id: 2,
        action: 'wait',
        target: 'iframe',
        text: 'iframe信息获取测试',
        dynamicText: {
            source: 'element',
            selector: 'iframe',
            property: 'src',
            template: '当前加载的页面是：{content}',
            fallback: '无法获取iframe信息'
        },
        duration: 3000
    },

    // 测试3: 获取iframe中的地图容器ID（跨域测试）
    {
        id: 3,
        action: 'wait',
        target: 'iframe',
        text: '跨域元素属性测试',
        dynamicText: {
            source: 'attribute',
            selector: '#map',
            property: 'id',
            template: 'iframe中地图容器的ID是：{content}',
            fallback: '无法获取iframe中的地图容器ID'
        },
        duration: 3000
    },

    // 测试4: 获取iframe中的图层控制标签文本
    {
        id: 4,
        action: 'wait',
        target: 'iframe',
        text: 'iframe中元素文本获取测试',
        dynamicText: {
            source: 'element',
            selector: '.layer-control-panel label:first-child',
            property: 'textContent',
            template: '第一个图层控制选项是：{content}',
            fallback: '无法获取图层控制选项'
        },
        duration: 3000
    },

    // 测试5: 计算值 - 统计iframe中的图层控制数量
    {
        id: 5,
        action: 'wait',
        target: 'iframe',
        text: '元素数量统计测试',
        dynamicText: {
            source: 'computed',
            selector: '.layer-control-panel label',
            property: 'count',
            template: 'iframe中共有{content}个图层控制选项',
            fallback: '无法统计图层控制选项'
        },
        duration: 3000
    },

    // 测试6: 获取当前时间（计算值测试）
    {
        id: 6,
        action: 'wait',
        target: 'body',
        text: '当前时间获取测试',
        dynamicText: {
            source: 'computed',
            selector: 'body',
            property: 'timestamp',
            template: '当前时间：{content}',
            fallback: '无法获取当前时间'
        },
        duration: 3000
    },

    // 测试7: 不存在的元素（测试错误处理）
    {
        id: 7,
        action: 'wait',
        target: 'body',
        text: '错误处理测试 - 这是兜底文本',
        dynamicText: {
            source: 'element',
            selector: '.non-existent-element',
            property: 'textContent',
            template: '找到了不存在的元素：{content}',
            fallback: '测试成功：正确处理了不存在的元素'
        },
        duration: 3000
    },

    // 测试8: 多元素组合文本（获取多个图层控制标签）
    {
        id: 8,
        action: 'wait',
        target: 'iframe',
        text: '多元素组合文本测试',
        dynamicText: {
            source: 'multiple',
            selectors: [
                { selector: '.layer-control-panel label:nth-child(1)', property: 'textContent' },
                { selector: '.layer-control-panel label:nth-child(2)', property: 'textContent' }
            ],
            template: '图层控制包含：{0} 和 {1} 等选项',
            fallback: '无法获取图层控制信息'
        },
        duration: 4000
    },

    // 测试9: 获取当前URL（计算值测试）
    {
        id: 9,
        action: 'wait',
        target: 'body',
        text: '当前URL获取测试',
        dynamicText: {
            source: 'computed',
            selector: 'body',
            property: 'url',
            template: '当前页面地址：{content}',
            fallback: '无法获取当前URL'
        },
        duration: 3000
    },

    // 测试10: 获取域名（计算值测试）
    {
        id: 10,
        action: 'wait',
        target: 'body',
        text: '域名获取测试',
        dynamicText: {
            source: 'computed',
            selector: 'body',
            property: 'domain',
            template: '当前域名：{content}',
            fallback: '无法获取域名'
        },
        duration: 3000
    }
];

// 扩展计算值功能，支持更多类型
if (window.presentationController) {
    // 扩展原有的getComputedValue方法
    const originalGetComputedValue = window.presentationController.getComputedValue;
    
    window.presentationController.getComputedValue = async function(selector, computeType) {
        try {
            switch (computeType) {
                case 'count':
                    const elements = document.querySelectorAll(selector);
                    return elements.length.toString();
                    
                case 'visible':
                    const element = document.querySelector(selector);
                    return element && element.offsetParent !== null ? '可见' : '隐藏';
                    
                case 'timestamp':
                    return new Date().toLocaleString('zh-CN');
                    
                case 'url':
                    return window.location.href;
                    
                case 'domain':
                    return window.location.hostname;
                    
                default:
                    // 调用原始方法
                    if (originalGetComputedValue) {
                        return await originalGetComputedValue.call(this, selector, computeType);
                    }
                    console.warn('未知的计算类型:', computeType);
                    return '';
            }
        } catch (error) {
            console.error('计算值获取失败:', error);
            return '';
        }
    };
}

// 使用方法示例：
// 1. 替换当前脚本进行测试：
//    window.presentationScript = window.presentationScriptDynamicTextTest;
//
// 2. 启动测试：
//    window.PresentationSystem.start();
//
// 3. 在控制台查看动态文本获取的日志输出

console.log('✅ 动态文本获取测试脚本已加载');
console.log('📝 使用方法:');
console.log('   window.presentationScript = window.presentationScriptDynamicTextTest;');
console.log('   window.PresentationSystem.start();');
