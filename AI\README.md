# 智能讲解系统

## 概述

智能讲解系统是一个基于Web技术的自动化演示工具，能够模拟真实的用户操作，配合语音解说和视觉高亮效果，为青稞种子数字化智慧化布局图系统提供沉浸式的产品演示体验。

## 功能特点

### 🎯 核心功能
- **自动化操作**: 模拟真实的用户点击、导航等操作
- **语音解说**: 基于Web Speech API的文字转语音功能
- **视觉高亮**: 智能高亮显示当前操作区域
- **流程控制**: 支持播放、暂停、停止、跳转等控制
- **容错处理**: 完善的错误处理和重试机制

### 🛠 技术特性
- **模块化设计**: 清晰的架构分层，易于维护和扩展
- **响应式界面**: 适配不同屏幕尺寸的设备
- **实时反馈**: 显示讲解进度和当前状态
- **键盘快捷键**: 支持快捷键操作控制

## 系统架构

```
智能讲解系统
├── PresentationController (主控制器)
│   ├── 流程管理
│   ├── 步骤执行
│   └── 状态控制
├── SpeechEngine (语音引擎)
│   ├── 文字转语音
│   ├── 语音控制
│   └── 语音选择
├── UIController (界面控制器)
│   ├── 控制面板
│   ├── 文本显示
│   └── 进度指示
├── ErrorHandler (错误处理器)
│   ├── 错误分类
│   ├── 重试机制
│   └── 容错策略
└── PresentationScript (讲解脚本)
    ├── 操作定义
    ├── 文本内容
    └── 时序控制
```

## 文件说明

### 核心文件
- `presentation-main.js` - 主入口文件，负责系统初始化
- `presentation-controller.js` - 主控制器，管理整个讲解流程
- `speech-engine.js` - 语音引擎，处理文字转语音
- `ui-controller.js` - UI控制器，管理讲解界面
- `error-handler.js` - 错误处理器，提供容错机制
- `presentation-script.js` - 讲解脚本，定义完整的演示流程

### 配置文件
- `README.md` - 系统说明文档

## 使用方法

### 1. 系统集成
在HTML页面中引入所有必要的脚本文件：

```html
<!-- 智能讲解系统 -->
<script src="AI/speech-engine.js"></script>
<script src="AI/error-handler.js"></script>
<script src="AI/ui-controller.js"></script>
<script src="AI/presentation-controller.js"></script>
<script src="AI/presentation-script.js"></script>
<script src="AI/presentation-main.js"></script>
```

### 2. 启动讲解
系统会自动在页面左上角（返回首页按钮右侧）添加"开启讲解模式"按钮，点击即可开始演示。

### 3. 控制操作
- **播放/暂停**: 点击控制面板的播放/暂停按钮
- **停止**: 点击停止按钮结束讲解
- **跳转**: 使用上一步/下一步按钮
- **快捷键**: 
  - `空格键` 或 `Esc` - 播放/暂停
  - `→` - 下一步
  - `←` - 上一步
  - `Ctrl+Shift+P` - 启动/停止讲解

## 讲解脚本格式

每个讲解步骤包含以下属性：

```javascript
{
    id: 1,                          // 步骤ID
    action: 'highlight',            // 操作类型
    target: '.element-selector',    // 目标元素
    text: '讲解文本内容',           // 解说词
    duration: 3000,                 // 持续时间(毫秒)
    speechOptions: {                // 语音选项
        rate: 0.8,                  // 语速
        pitch: 1                    // 音调
    },
    options: {                      // 额外选项
        waitFor: '.result-element', // 等待元素出现
        timeout: 5000               // 超时时间
    }
}
```

### 支持的操作类型
- `highlight` - 高亮显示元素
- `click` - 点击元素
- `navigate` - 页面导航
- `wait` - 等待元素出现
- `scroll` - 滚动到元素
- `input` - 输入文本

## 自定义配置

### 修改讲解内容
编辑 `presentation-script.js` 文件，修改 `window.presentationScript` 数组中的步骤定义。

### 语音配置详细说明

#### 1. 快速配置语音
系统提供了便捷的语音配置方法：

```javascript
// 在浏览器控制台中执行
window.presentationController.speechEngine.configure(
    'Microsoft Yaoyao',  // 语音名称
    0.7,                 // 语速 (0.1-10，推荐0.5-1.5)
    1,                   // 音调 (0-2，推荐0.8-1.2)
    0.8                  // 音量 (0-1)
);
```

#### 2. 推荐的中文语音配置

**Microsoft Yaoyao (推荐)**
- 特点：女声，清晰自然，最适合讲解
- 配置：`{ voiceName: 'Microsoft Yaoyao', rate: 0.7, pitch: 1 }`

**Microsoft Huihui**
- 特点：女声，温和亲切
- 配置：`{ voiceName: 'Microsoft Huihui', rate: 0.8, pitch: 1 }`

**Microsoft Kangkang**
- 特点：男声，稳重专业
- 配置：`{ voiceName: 'Microsoft Kangkang', rate: 0.7, pitch: 0.9 }`

#### 3. 查看可用语音
```javascript
// 获取推荐配置和所有可用语音
const voiceInfo = window.presentationController.speechEngine.getRecommendedVoices();
console.log('推荐配置:', voiceInfo.recommended);
console.log('可用语音:', voiceInfo.available);
```

#### 4. 语音参数说明

**语速 (rate)**
- 范围：0.1 - 10
- 推荐值：0.5 - 1.5
- 0.7：适中偏慢，适合讲解
- 1.0：正常语速
- 1.5：较快语速

**音调 (pitch)**
- 范围：0 - 2
- 推荐值：0.8 - 1.2
- 1.0：正常音调
- 0.9：略低沉
- 1.1：略高亢

**音量 (volume)**
- 范围：0 - 1
- 推荐值：0.6 - 1.0
- 0.8：适中音量

#### 5. 修改默认配置
在 `speech-engine.js` 中修改 `defaultOptions` 对象：

```javascript
this.defaultOptions = {
    rate: 0.7,                    // 语速
    pitch: 1,                     // 音调
    volume: 0.8,                  // 音量
    lang: 'zh-CN',               // 语言
    voiceName: 'Microsoft Yaoyao' // 指定语音名称
};
```

### 视觉效果配置

#### 调整高亮遮罩透明度
在 `presentation-controller.js` 的 `highlightElement` 方法中修改：

```javascript
// 调整背景遮罩透明度 (0.1-0.8)
background: rgba(0, 0, 0, 0.2);  // 0.2 = 较浅，0.5 = 适中，0.8 = 较深
```

#### 调整高亮边框样式
```javascript
// 修改高亮边框颜色和效果
border: 3px solid #00ff88;                    // 边框颜色
box-shadow: 0 0 20px rgba(0, 255, 136, 0.6); // 发光效果
```

### 按钮位置配置
在 `presentation-main.js` 中修改启动按钮位置：

```javascript
// 调整按钮位置
top: 15px;    // 距离顶部距离
left: 85px;   // 距离左侧距离（在返回首页按钮右侧）
```

### 自定义样式
UI控制器中的样式可以通过修改 `addStyles()` 方法中的CSS来调整。

## 错误处理

系统提供多层次的错误处理机制：

1. **元素未找到**: 自动等待、使用备用选择器、跳过步骤
2. **操作超时**: 增加超时时间重试
3. **网络错误**: 检测网络状态，等待恢复
4. **语音错误**: 禁用语音继续文字展示
5. **点击错误**: 尝试不同的点击方式

## 浏览器兼容性

- **Chrome**: 完全支持
- **Firefox**: 完全支持
- **Safari**: 支持（语音功能可能有限制）
- **Edge**: 完全支持

## 注意事项

1. **语音功能**: 依赖浏览器的Web Speech API，某些浏览器可能需要用户交互后才能播放语音
2. **元素选择器**: 确保讲解脚本中的选择器与实际页面元素匹配
3. **时序控制**: 根据页面加载速度调整步骤间的等待时间
4. **权限要求**: 某些浏览器可能需要用户授权才能使用语音功能

## 开发指南

### 添加新的操作类型
在 `PresentationController` 的 `executeStep` 方法中添加新的 case：

```javascript
case 'custom_action':
    await this.customAction(step.target, step.options);
    break;
```

### 扩展错误处理
在 `ErrorHandler` 的 `getErrorStrategy` 方法中添加新的错误类型判断。

### 自定义UI组件
修改 `UIController` 中的相关方法来添加新的界面元素。

## 版本历史

- **v2.0.0** - 动态文本获取版本 (2024年)
  - ✅ **新增动态文本获取功能**: 支持从页面元素实时获取文本内容
  - ✅ **跨域支持**: 支持iframe和第三方页面的元素访问
  - ✅ **多种获取方式**: 支持元素文本、属性值、计算值、多元素组合
  - ✅ **完善的兜底机制**: 获取失败时自动使用静态文本
  - ✅ **执行策略优化**: 新增多种执行策略和最佳实践
  - ✅ **文档统一**: 合并execution-strategy-guide.md到主文档
  - ✅ **测试脚本**: 提供完整的动态文本功能测试用例

- **v1.0.0** - 初始版本，包含基础的讲解功能
  - 自动化操作执行
  - 语音解说功能
  - 视觉高亮效果
  - 基础错误处理

## 📚 文档结构

### 主要文档
- **`presentation-script-guide.md`** - 完整使用指南（推荐阅读）
  - 包含所有功能的详细说明
  - 动态文本获取功能详解
  - 执行策略与最佳实践
  - 完整的示例代码和快速开始指南

### 测试文件
- **`presentation-script-dynamic-text-test.js`** - 动态文本功能测试脚本
  - 10个测试用例覆盖各种场景
  - 验证跨域功能
  - 可直接运行测试

### 使用建议
1. 首次使用请阅读 `presentation-script-guide.md`
2. 运行测试脚本验证功能：
   ```javascript
   window.presentationScript = window.presentationScriptDynamicTextTest;
   window.PresentationSystem.start();
   ```
3. 根据需要创建自定义脚本

## 技术支持

如有问题或建议，请联系开发团队。

详细使用说明请参考 `presentation-script-guide.md`。
