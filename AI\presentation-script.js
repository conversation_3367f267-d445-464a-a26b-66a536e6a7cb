/**
 * 讲解脚本 - 定义完整的讲解流程
 */
window.presentationScript = [
    // {
    //     id: 1,
    //     action: 'highlight',
    //     target: '#map',
    //     text: '欢迎来到青稞种子数字化智慧化布局图系统！这是我们的核心功能区域。在这里可以查看西藏各个地区的青稞种植分布和监测设备部署情况。',
    //     duration: 3000
    // },
    // {
    //     id: 2,
    //     action: 'highlight',
    //     target: '.project-intro-panel',
    //     text: '左侧是项目介绍面板，展示了西藏地区青稞种植的智慧农业发展情况。',
    //     duration: 3000
    // },
    // {
    //     id: 3,
    //     action: 'highlight',
    //     target: '.layer-control-panel',
    //     text: '右上角是图层控制面板，可以切换不同的地图图层，包括卫星地图、中国地图和市县边界等。',
    //     duration: 3000
    // },
    // {
    //     id: 4,
    //     action: 'highlight',
    //     target: '.main-bottom-nav',
    //     text: '底部是导航标签页，可以在"点位分布"和"青稞实况"之间切换，查看不同的业务内容。',
    //     duration: 3000
    // },
    // {
    //     id: 5,
    //     action: 'highlight',
    //     target: '#viewProjectBtn',
    //     text: '"定位项目区域"按钮，可以快速定位到项目覆盖的主要区域。',
    //     duration: 3000
    // },
    // {
    //     id: 6,
    //     action: 'highlight',
    //     target: '.linzhou-glow',
    //     text: '现在我们点击任一区县，查看区县下具体的设备分布情况。',
    //     duration: 3000
    // },
    // {
    //     id: 7,
    //     action: 'click',
    //     target: {
    //         selector: '.linzhou-glow',
    //         text: '林周县',
    //         //xpath:'/html/body/div[2]/div/div[7]/div[1]/div[2]/svg/g/path[39]',
    //         fallbackTarget: '#leaflet-tooltip-214'
    //     },
    //     text: '当前打开的是林周县的设备分布情况。',
    //     duration: 6000,
    //     options: {
    //         skipIfNotFound: true
    //     }
    // },
    // {
    //     id: 8,
    //     action: 'highlight',
    //     target: '#device-legend-panel',
    //     // 包括气象监测站、土壤墒情监测、智能孢子仪和虫情信息采集设备
    //     text: '可以看到右下角，设备图例面板，显示了该地区部署的各种监测设备类型和数量。',
    //     duration: 8000
    // },
    // {
    //     id: 9,
    //     action: 'highlight',
    //     target: '.project-nav-wrap',
    //     text: '左侧是区县导航面板，可以快速切换到其他县区查看设备情况。最上面是返回主视图的按钮。',
    //     duration: 3500
    // },
    // {
    //     id: 10,
    //     action: 'click',
    //     target: '.project-nav-back-btn',
    //     fallbackTarget: '.project-nav-wrap button:first-child',
    //     text: '现在让我们点击返回主视图按钮，回到整体的地图界面。',
    //     duration: 2000
    // },
    // {
    //     id: 11,
    //     action: 'highlight',
    //     target: '.main-bottom-nav',
    //     text: '接下来我们通过底部的导航栏，点击青稞实况菜单。',
    //     duration: 4000
    // },
    // {
    //     id: 12,
    //     action: 'click',
    //     target: 'a[href="realtime.html"]',
    //     text: '',
    //     duration: 5000,
    //     options: {
    //         waitFor: '.qingke-overview',
    //         timeout: 8000,
    //         skipIfNotFound: true
    //     }
    // },
    // {
    //     id: 13,
    //     action: 'wait',
    //     target: '.video-wrapper',
    //     text: '正在加载青稞实况页面...',
    //     timeout: 5000,
    //     duration: 2000
    // },
    // {
    //     id: 14,
    //     action: 'highlight',
    //     target: '.video-monitor-container',
    //     text: '欢迎来到青稞实况页面！这里展示了各个点位部署的实施监控画面。',
    //     duration: 5000
    // },
    // {
    //     id: 15,
    //     action: 'highlight',
    //     target: '.monitor-nav-panel',
    //     text: '左侧显示了我们当前在拉萨市、日喀则市下的各区县内，部署的监控点情况。',
    //     duration: 4000,
    //     options: {
    //         skipIfNotFound: true
    //     }
    // },
    // {
    //     id: 16,
    //     action: 'highlight',
    //     target: '.monitor-search',
    //     text: '左侧顶部提供了搜索和筛选功能，可以按照设备名称或者ID号，快速查找指定的监控设备。',
    //     duration: 3500,
    //     options: {
    //         skipIfNotFound: true
    //     }
    // },
    // {
    //     id: 17,
    //     action: 'highlight',
    //     target: '.monitor-grid-switch',
    //     text: '在其下方是控制右侧监控画面布局的按钮，包括单一画面、四宫格、九宫格，以及全屏。',
    //     duration: 3500,
    //     options: {
    //         skipIfNotFound: true
    //     }
    // },
    // {
    //     id: 18,
    //     action: 'click',
    //     target: '.monitor-device-item online',
    //     text: '我们可以点击任何在线的监控点，并通过右侧来查看实时视频监控画面。',
    //     duration: 2000,
    //     options: {
    //         waitFor: '.monitor-video',
    //         timeout: 5000,
    //         skipIfNotFound: true
    //     }
    // },
    // {
    //     id: 19,
    //     action: 'highlight',
    //     target: '.monitor-video',
    //     text: '现在可以看到实时的视频监控画面，这让我们能够直观地观察青稞种植区域的实际情况，实现远程监控和管理。',
    //     duration: 5000,
    //     options: {
    //         skipIfNotFound: true
    //     }
    // },
    // {
    //     id: 20,
    //     action: 'highlight',
    //     target: 'body',
    //     text: '以上就是青稞种子数字化智慧化布局图系统的主要功能演示。系统集成了地图展示、设备监控、数据分析和实时视频等多项功能，为青稞种植的智慧化管理提供了全面的技术支持。',
    //     duration: 6000
    // },
    // {
    //     id: 21,
    //     action: 'highlight',
    //     target: 'body',
    //     text: '感谢您观看本次系统演示！',
    //     duration: 4000,
    //     speech: true
    // },


    {
        id: 20,
        action: 'highlight',
        target: '.home-btn',
        text: '接下来我们点击返回首页按钮，回到门户主页。',
        duration: 3000,
        options: {
            crossDomain: false, 
        }
    },
    {
        id: 18,
        action: 'click',
        target: '.home-btn',
        text: '',
        duration: 2000,
        options: {
            crossDomain: false, 
        }
    },
    {
        id: 19,
        action: 'wait',
        text: '等待第三方页面加载完成...',
        duration: 5000,
        options: {
            crossDomain: true,
            forceExtension: true,
            skipIfNotFound: true,
            waitForElement: '.timer',
            maxWaitTime: 5000
        }
    },
    {
        id: 20,
        action: 'highlight',
        target: '.timer',
        text: '这里展示的是系统的名称！',
        duration: 6000,
        dynamicText: {
            source: 'element',
            selector: '.timer',
            property: 'textContent',
            template: '当前的系统名称是：{content}',
            fallback: '无法获取当前系统的名称！'
        },
        options: {
            crossDomain: true,
            forceExtension: true,
            skipIfNotFound: true
        }
    },
    {
        id: 20,
        action: 'highlight',
        target: '.bggrap1',
        text: '这个系统包含以下几个子系统！',
        duration: 6000,
        dynamicText: {
            source: 'multiple',
            selectors: [
                { selector: '.bggrap1', property: 'count' },
                { selector: '.col2:nth-of-type(1) div.text', property: 'textContent' },
                { selector: '.col2:nth-of-type(2) div.text', property: 'textContent' },
                { selector: '.col2:nth-of-type(3) div.text', property: 'textContent' },
                { selector: '.col2:nth-of-type(4) div.text', property: 'textContent' },
            ],
            template: '可以看到当前系统共包含：{0} 个子系统，分别是：{1}、{2}、{3}、{4}',
            // selector: '.bggrap1',
            // property: 'count',
            // template: '可以看到当前系统共包含：{content} 个子系统',
            fallback: '无法获取当前子系统的具体数量！'
        },
        options: {
            crossDomain: true,
            forceExtension: true,
            skipIfNotFound: true
        }
    },
    {
        id: 20,
        action: 'highlight',
        target: {
            selector: '.col2:nth-of-type(2) img.image.image2',
            fallbackTarget: 'img.image.image2:nth-of-type(2)'
        },
        text: '现在进入第二个系统—请客种子种植监测一张图！',
        duration: 6000,
        options: {
            skipIfNotFound: true,
            // 🎯 新增：明确指定这是跨域iframe中的元素，优先使用扩展
            crossDomain: true,
            forceExtension: true
            // 或者直接强制使用扩展：forceExtension: true
        }
    },
    {
        id: 21,
        action: 'click',
        target: {
            selector: '.col2:nth-of-type(2) img.image.image2',
            fallbackTarget: 'img.image.image2:nth-of-type(2)'
        },
        text: '',
        duration: 6000,
        options: {
            skipIfNotFound: true,
            forceExtension: true,
            // 🎯 新增：明确指定这是跨域iframe中的元素，优先使用扩展
            crossDomain: true
        }
    },
];

// 导出脚本供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.presentationScript;
}

