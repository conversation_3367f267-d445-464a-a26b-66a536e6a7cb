/**
 * 智能讲解系统 - 主控制器
 * 负责管理整个讲解流程的执行
 */
class PresentationController {
    constructor() {
        this.isActive = false;
        this.currentStep = 0;
        this.script = [];
        this.speechEngine = new SpeechEngine();
        this.uiController = new UIController();
        this.errorHandler = new ErrorHandler();
        this.isPaused = false;
        this.isDestroyed = false;
        this.isPlaying = false;
        this.isProcessing = false; // 添加处理状态标志，防止快速点击
        this.hasExecutedCurrentStep = false; // 标记当前步骤是否已执行操作
        this.currentStepTimer = null; // 当前步骤的定时器

        // 浏览器插件支持
        this.extensionAvailable = false;
        this.extensionId = null; // 插件ID将在检测时确定

        // 检测浏览器插件
        this.detectExtension();
    }

    /**
     * 初始化讲解系统
     */
    async init() {
        try {
            // 加载讲解脚本
            this.script = await this.loadScript();
            
            // 初始化UI界面
            await this.uiController.init();
            
            // 绑定控制事件
            this.bindEvents();
            

            return true;
        } catch (error) {
            console.error('讲解系统初始化失败:', error);
            return false;
        }
    }

    /**
     * 检测浏览器插件是否可用
     */
    async detectExtension() {
        try {
            console.log('🔍 检测浏览器扩展...');

            // 检查扩展桥接API是否可用
            console.log('🔍 当前window.intelligentPresentationExtensionBridge:', window.intelligentPresentationExtensionBridge);

            if (!window.intelligentPresentationExtensionBridge) {
                console.log('⏳ 等待扩展桥接API加载...');

                // 监听桥接API准备好的事件
                const bridgeReady = new Promise(resolve => {
                    const handler = () => {
                        console.log('🎉 收到桥接API准备好事件');
                        window.removeEventListener('intelligentPresentationBridgeReady', handler);
                        resolve();
                    };
                    window.addEventListener('intelligentPresentationBridgeReady', handler);

                    // 设置超时
                    setTimeout(() => {
                        window.removeEventListener('intelligentPresentationBridgeReady', handler);
                        resolve();
                    }, 3000);
                });

                await bridgeReady;
                console.log('🔍 等待后window.intelligentPresentationExtensionBridge:', window.intelligentPresentationExtensionBridge);
            }

            if (!window.intelligentPresentationExtensionBridge) {
                console.log('🔌 扩展桥接API不可用');
                console.log('🔍 window对象上的所有属性:', Object.keys(window).filter(key => key.includes('intelligent')));
                return;
            }

            // 尝试发送测试消息到扩展
            try {
                const testResult = await this.sendExtensionMessage({
                    action: 'getInfo',
                    data: {}
                });

                if (testResult && testResult.success) {
                    this.extensionAvailable = true;
                    console.log('✅ 智能讲解插件检测成功，使用桥接API通信');
                    console.log('🔍 扩展信息:', testResult);
                    return;
                }
            } catch (error) {
                console.log('⚠️ 扩展消息测试失败:', error.message);
            }

            // 等待一段时间让内容脚本加载
            console.log('⏳ 等待扩展加载...');
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 再次尝试
            try {
                const testResult = await this.sendExtensionMessage({
                    action: 'getInfo',
                    data: {}
                });

                if (testResult && testResult.success) {
                    this.extensionAvailable = true;
                    console.log('✅ 智能讲解插件检测成功（延迟检测），使用桥接API通信');
                    console.log('🔍 扩展信息:', testResult);
                    return;
                }
            } catch (error) {
                console.log('⚠️ 延迟扩展消息测试失败:', error.message);
            }

            console.log('🔌 智能讲解扩展未安装或未启用，将使用传统模式');
            console.log('💡 如需跨域控制功能，请安装并启用智能讲解助手扩展');

        } catch (error) {
            console.log('🔌 插件检测失败:', error.message, '将使用传统模式');
        }
    }

    /**
     * 发送消息到扩展（通过桥接API）
     */
    async sendExtensionMessage(message) {
        if (!window.intelligentPresentationExtensionBridge) {
            throw new Error('扩展桥接API不可用');
        }

        console.log('📨 发送扩展消息:', message);

        // 根据消息类型调用对应的桥接API方法
        const { action, data } = message;

        switch (action) {
            case 'highlight':
                return window.intelligentPresentationExtensionBridge.highlight(data.selector, {
                    color: data.color,
                    duration: data.duration
                });
            case 'click':
                return window.intelligentPresentationExtensionBridge.click(data.selector, {
                    showIndicator: data.showIndicator
                });
            case 'simulateClick':
                return window.intelligentPresentationExtensionBridge.simulateClick(data.selector, data);
            case 'clearHighlight':
                return window.intelligentPresentationExtensionBridge.clearHighlight();
            case 'getInfo':
                return window.intelligentPresentationExtensionBridge.getInfo();
            default:
                throw new Error('未知的扩展操作: ' + action);
        }
    }

    /**
     * 显示插件安装提示
     */
    showExtensionInstallPrompt() {
        // 创建提示界面
        const promptHtml = `
            <div id="extension-install-prompt" style="
                position: fixed;
                top: 20px;
                right: 20px;
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 8px;
                padding: 16px;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            ">
                <div style="display: flex; align-items: center; margin-bottom: 12px;">
                    <span style="font-size: 20px; margin-right: 8px;">🔌</span>
                    <strong style="color: #856404;">需要安装浏览器插件</strong>
                </div>
                <p style="margin: 0 0 12px 0; color: #856404; font-size: 14px;">
                    为了在iframe中操作第三方页面，请安装"智能讲解助手"插件。
                </p>
                <div style="display: flex; gap: 8px;">
                    <button id="install-extension-btn" style="
                        background: #007bff;
                        color: white;
                        border: none;
                        padding: 6px 12px;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 12px;
                    ">安装插件</button>
                    <button id="close-prompt-btn" style="
                        background: #6c757d;
                        color: white;
                        border: none;
                        padding: 6px 12px;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 12px;
                    ">稍后提醒</button>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', promptHtml);

        // 绑定事件
        document.getElementById('install-extension-btn').addEventListener('click', () => {
            window.open('chrome://extensions/', '_blank');
        });

        document.getElementById('close-prompt-btn').addEventListener('click', () => {
            document.getElementById('extension-install-prompt').remove();
        });

        // 5秒后自动隐藏
        setTimeout(() => {
            const prompt = document.getElementById('extension-install-prompt');
            if (prompt) prompt.remove();
        }, 10000);
    }

    /**
     * 通过插件执行操作
     */
    async executeWithExtension(action, params = {}) {
        if (!this.extensionAvailable) {
            throw new Error('浏览器插件不可用');
        }

        try {
            let result;
            console.log(`🔌 执行扩展操作: ${action}, 参数:`, params);

            switch (action) {
                case 'highlight':
                    if (this.extensionAvailable) {
                        console.log('🎨 调用扩展高亮功能（消息API）');
                        result = await this.sendExtensionMessage({
                            action: 'highlight',
                            data: {
                                selector: params.selector,
                                color: params.color || '#ffeb3b',
                                duration: params.duration || 3000
                            }
                        });
                        console.log('🎨 扩展高亮结果:', result);
                    } else {
                        console.error('❌ 扩展不可用，高亮功能不可用');
                        throw new Error('高亮功能不可用');
                    }
                    break;

                case 'click':
                    if (this.extensionAvailable) {
                        console.log('🖱️ 调用扩展点击功能（消息API）');
                        result = await this.sendExtensionMessage({
                            action: 'click',
                            data: {
                                selector: params.selector,
                                showIndicator: params.showIndicator !== false
                            }
                        });
                        console.log('🖱️ 扩展点击结果:', result);
                    } else {
                        console.error('❌ 扩展不可用，点击功能不可用');
                        throw new Error('点击功能不可用');
                    }
                    break;

                case 'removeHighlight':
                    if (typeof window.intelligentPresentationClearHighlight === 'function') {
                        result = window.intelligentPresentationClearHighlight();
                    } else {
                        throw new Error('清除高亮功能不可用');
                    }
                    break;

                case 'getInfo':
                    if (typeof window.intelligentPresentationGetInfo === 'function') {
                        result = window.intelligentPresentationGetInfo();
                    } else {
                        result = {
                            success: true,
                            data: {
                                title: document.title,
                                url: window.location.href,
                                isIframe: window !== window.top
                            }
                        };
                    }
                    break;

                default:
                    throw new Error('不支持的操作: ' + action);
            }

            if (!result || !result.success) {
                throw new Error(result?.error || '操作失败');
            }

            return result.data || result;

        } catch (error) {
            console.error('插件操作失败:', error);
            throw error;
        }
    }

    /**
     * 高亮元素（支持插件）
     */
    async highlightElement(target, options = {}) {
        const defaultOptions = {
            color: '#ffeb3b',
            duration: 3000
        };

        const finalOptions = { ...defaultOptions, ...options };

        // 解析目标选择器（支持字符串或对象格式）
        const { primarySelector, fallbackSelector } = this.parseTarget(target);

        // 准备要尝试的选择器列表
        const selectorsToTry = [primarySelector];
        if (fallbackSelector) {
            selectorsToTry.push(fallbackSelector);
        }

        // 🎯 新增：检查执行策略
        const strategy = this.determineExecutionStrategy(options);
        console.log('🎯 执行策略:', strategy);

        // 🎯 根据策略执行
        return await this.executeWithStrategy(selectorsToTry, strategy, 'highlight', finalOptions, options);
    }

    /**
     * 🎯 根据策略执行操作
     */
    async executeWithStrategy(selectorsToTry, strategy, action, actionOptions, globalOptions = {}) {
        for (let i = 0; i < selectorsToTry.length; i++) {
            const currentSelector = selectorsToTry[i];
            const isMainSelector = i === 0;
            console.log(`📄 尝试${isMainSelector ? '主' : '备用'}选择器 [${strategy}]:`, currentSelector);

            const result = await this.trySelector(currentSelector, strategy, action, actionOptions, isMainSelector);
            if (result.success) {
                return result;
            }
        }

        // 所有选择器都失败了
        console.error('❌ 所有选择器都失败了:', selectorsToTry);
        if (globalOptions.skipIfNotFound) {
            console.warn(`⚠️ 跳过未找到的元素，已尝试选择器:`, selectorsToTry);
            return { success: false, skipped: true };
        } else {
            console.warn(`⚠️ 继续流程，但跳过操作，已尝试选择器:`, selectorsToTry);
            return { success: false, skipped: true, reason: 'all_selectors_failed' };
        }
    }

    /**
     * 🎯 尝试单个选择器
     */
    async trySelector(selector, strategy, action, options, isMainSelector) {
        switch (strategy) {
            case 'extension_only':
                return await this.tryExtensionMethod(selector, action, options, isMainSelector);

            case 'traditional_only':
                return this.tryTraditionalMethod(selector, action, options, isMainSelector);

            case 'extension_first':
                let result = await this.tryExtensionMethod(selector, action, options, isMainSelector);
                if (!result.success) {
                    result = this.tryTraditionalMethod(selector, action, options, isMainSelector);
                }
                return result;

            case 'traditional_first':
            case 'auto':
            default:
                let traditionalResult = this.tryTraditionalMethod(selector, action, options, isMainSelector);
                if (!traditionalResult.success && this.extensionAvailable) {
                    traditionalResult = await this.tryExtensionMethod(selector, action, options, isMainSelector);
                }
                return traditionalResult;
        }
    }

    /**
     * 🎯 尝试扩展方法
     */
    async tryExtensionMethod(selector, action, options, isMainSelector) {
        if (!this.extensionAvailable) {
            console.warn('🔌 扩展不可用');
            return { success: false, reason: 'extension_not_available' };
        }

        try {
            console.log(`🔌 使用扩展方法${action}，${isMainSelector ? '主' : '备用'}选择器:`, selector);

            let extensionOptions = {};
            if (action === 'highlight') {
                extensionOptions = {
                    selector: selector,
                    color: options.color,
                    duration: options.duration
                };
            } else if (action === 'click') {
                extensionOptions = {
                    selector: selector,
                    showIndicator: options.showIndicator !== false
                };
            }

            const result = await this.executeWithExtension(action, extensionOptions);
            console.log(`✅ 扩展方法${action}成功，使用${isMainSelector ? '主' : '备用'}选择器:`, selector);
            return result;
        } catch (error) {
            console.warn(`🔌 扩展方法${action}失败，选择器: ${selector}`, error.message);
            return { success: false, reason: 'extension_failed', error: error.message };
        }
    }

    /**
     * 🎯 尝试传统方法
     */
    tryTraditionalMethod(selector, action, options, isMainSelector) {
        try {
            console.log(`📄 使用传统方法${action}，${isMainSelector ? '主' : '备用'}选择器:`, selector);

            let result;
            if (action === 'highlight') {
                result = this.highlightElementTraditional(selector, options);
            } else if (action === 'click') {
                result = this.clickElementTraditional(selector, options);
            }

            if (result.success) {
                console.log(`✅ 传统方法${action}成功，使用${isMainSelector ? '主' : '备用'}选择器:`, selector);
            } else {
                console.warn(`📄 传统方法${action}失败，选择器:`, selector);
            }
            return result;
        } catch (error) {
            console.warn(`📄 传统方法${action}异常，选择器: ${selector}`, error.message);
            return { success: false, reason: 'traditional_failed', error: error.message };
        }
    }

    /**
     * 解析目标选择器（支持字符串或对象格式）
     */
    parseTarget(target) {
        if (typeof target === 'string') {
            return { primarySelector: target, fallbackSelector: null };
        }

        if (typeof target === 'object' && target !== null) {
            const primarySelector = target.selector || target.xpath || target.text;
            const fallbackSelector = target.fallbackTarget;
            return { primarySelector, fallbackSelector };
        }

        throw new Error('Invalid target format');
    }

    /**
     * 🎯 确定执行策略
     */
    determineExecutionStrategy(options) {
        // 1. 显式指定策略
        if (options.forceExtension === true) {
            return 'extension_only';
        }
        if (options.forceTraditional === true) {
            return 'traditional_only';
        }

        // 2. 根据环境自动判断
        if (options.crossDomain === true) {
            return this.extensionAvailable ? 'extension_first' : 'extension_only_fail';
        }
        if (options.crossDomain === false) {
            return 'traditional_first';
        }

        // 3. 默认策略（智能判断）
        return 'auto';
    }

    /**
     * 点击元素（支持插件）
     */
    async clickElement(target, options = {}) {
        // 解析目标选择器（支持字符串或对象格式）
        const { primarySelector, fallbackSelector } = this.parseTarget(target);

        // 准备要尝试的选择器列表
        const selectorsToTry = [primarySelector];
        if (fallbackSelector) {
            selectorsToTry.push(fallbackSelector);
        }

        // 🎯 检查执行策略
        const strategy = this.determineExecutionStrategy(options);
        console.log('🎯 点击执行策略:', strategy);

        // 🎯 根据策略执行
        return await this.executeWithStrategy(selectorsToTry, strategy, 'click', options, options);
    }

    /**
     * 滚动到元素（支持插件）
     */
    async scrollToElement(selector, options = {}) {
        try {
            if (this.extensionAvailable) {
                // 使用插件滚动
                console.log('🔌 使用插件滚动到元素:', selector);
                return await this.executeWithExtension('scroll', {
                    selector: selector,
                    behavior: options.behavior || 'smooth',
                    block: options.block || 'center'
                });
            } else {
                // 降级到传统方法
                console.log('📄 使用传统方法滚动到元素:', selector);
                return this.scrollToElementTraditional(selector, options);
            }
        } catch (error) {
            console.error('滚动到元素失败:', error);
            // 如果插件失败，尝试传统方法
            if (this.extensionAvailable) {
                console.log('🔄 插件失败，降级到传统方法');
                return this.scrollToElementTraditional(selector, options);
            }
            throw error;
        }
    }

    /**
     * 清除所有高亮（支持插件）
     */
    async clearAllHighlights() {
        try {
            if (this.extensionAvailable) {
                console.log('🔌 使用插件清除高亮（消息API）');
                await this.sendExtensionMessage({
                    action: 'clearHighlight',
                    data: {}
                });
            }

            // 同时清除传统方法的高亮
            this.clearHighlightsTraditional();

        } catch (error) {
            console.error('清除高亮失败:', error);
            // 至少清除传统方法的高亮
            this.clearHighlightsTraditional();
        }
    }

    /**
     * 传统方法：高亮元素（降级方案）
     */
    highlightElementTraditional(selector, options) {
        try {
            console.log(`🔍 传统方法查找元素: ${selector}`);

            // 尝试在当前页面查找元素
            const element = document.querySelector(selector);
            console.log(`📄 主页面查找结果:`, element ? '找到' : '未找到');
            if (element) {
                this.addHighlightStyle(element, options.color, options.duration);
                return { success: true, method: 'traditional' };
            }

            // 尝试在iframe中查找
            const iframes = document.querySelectorAll('iframe');
            console.log(`🖼️ 找到 ${iframes.length} 个iframe`);

            for (let i = 0; i < iframes.length; i++) {
                const iframe = iframes[i];
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const iframeElement = iframeDoc.querySelector(selector);
                    console.log(`📄 iframe[${i}] 查找结果:`, iframeElement ? '找到' : '未找到');

                    if (iframeElement) {
                        this.addHighlightStyle(iframeElement, options.color, options.duration);
                        return { success: true, method: 'traditional-iframe' };
                    }
                } catch (error) {
                    console.log(`⚠️ iframe[${i}] 无法访问（跨域限制）:`, iframe.src);
                }
            }

            return { success: false, error: '未找到目标元素' };

        } catch (error) {
            console.error('传统高亮方法失败:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 传统方法：点击元素（降级方案）
     */
    clickElementTraditional(selector, options) {
        try {
            console.log(`🔍 传统方法点击元素: ${selector}`);

            // 尝试在当前页面查找元素
            const element = document.querySelector(selector);
            console.log(`📄 主页面查找结果:`, element ? '找到' : '未找到');
            if (element) {
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                setTimeout(() => element.click(), 500);
                return { success: true, method: 'traditional' };
            }

            // 尝试在iframe中查找
            const iframes = document.querySelectorAll('iframe');
            console.log(`🖼️ 找到 ${iframes.length} 个iframe`);

            for (let i = 0; i < iframes.length; i++) {
                const iframe = iframes[i];
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const iframeElement = iframeDoc.querySelector(selector);
                    console.log(`📄 iframe[${i}] 查找结果:`, iframeElement ? '找到' : '未找到');

                    if (iframeElement) {
                        iframeElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        setTimeout(() => iframeElement.click(), 500);
                        return { success: true, method: 'traditional-iframe' };
                    }
                } catch (error) {
                    console.log(`⚠️ iframe[${i}] 无法访问（跨域限制）:`, iframe.src);
                }
            }

            return { success: false, error: '未找到目标元素' };

        } catch (error) {
            console.error('传统点击方法失败:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 传统方法：滚动到元素（降级方案）
     */
    scrollToElementTraditional(selector, options) {
        try {
            // 尝试在当前页面查找元素
            const element = document.querySelector(selector);
            if (element) {
                element.scrollIntoView({
                    behavior: options.behavior || 'smooth',
                    block: options.block || 'center'
                });
                return { success: true, method: 'traditional' };
            }

            // 尝试在iframe中查找
            const iframes = document.querySelectorAll('iframe');
            for (const iframe of iframes) {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const iframeElement = iframeDoc.querySelector(selector);
                    if (iframeElement) {
                        iframeElement.scrollIntoView({
                            behavior: options.behavior || 'smooth',
                            block: options.block || 'center'
                        });
                        return { success: true, method: 'traditional-iframe' };
                    }
                } catch (error) {
                    console.warn('无法访问iframe内容（跨域限制）:', iframe.src);
                }
            }

            throw new Error('未找到目标元素');

        } catch (error) {
            console.error('传统滚动方法失败:', error);
            throw error;
        }
    }

    /**
     * 添加高亮样式
     */
    addHighlightStyle(element, color, duration) {
        // 创建高亮样式 - 只使用边框，不影响内容
        element.style.outline = `3px solid ${color}`;
        element.style.outlineOffset = '2px';
        element.style.transition = 'all 0.3s ease';

        // 添加标记类，便于清除
        element.classList.add('presentation-highlight-traditional');

        // 添加到高亮列表
        this.highlightedElements.push(element);

        // 自动移除高亮
        if (duration > 0) {
            setTimeout(() => {
                this.removeHighlightStyle(element);
            }, duration);
        }
    }

    /**
     * 移除高亮样式
     */
    removeHighlightStyle(element) {
        element.style.outline = '';
        element.style.outlineOffset = '';
        element.style.transition = '';
        element.classList.remove('presentation-highlight-traditional');

        // 从高亮列表中移除
        const index = this.highlightedElements.indexOf(element);
        if (index > -1) {
            this.highlightedElements.splice(index, 1);
        }
    }

    /**
     * 清除传统方法的高亮
     */
    clearHighlightsTraditional() {
        this.highlightedElements.forEach(element => {
            this.removeHighlightStyle(element);
        });
        this.highlightedElements = [];
    }

    /**
     * 开始讲解
     */
    async start() {
        if (this.isActive) {
            console.warn('讲解已在进行中');
            return;
        }

        // 设置用户交互标志，允许语音播放
        window.speechUserInteraction = true;


        // 加载脚本
        this.script = await this.loadScript();

        if (!this.script || this.script.length === 0) {
            console.error('无法启动讲解：脚本为空');
            return;
        }

        this.isActive = true;
        this.isPlaying = true;
        this.currentStep = 0;
        this.isPaused = false;
        this.isDestroyed = false;
        this.hasExecutedCurrentStep = false;

        // 初始化语音引擎
        try {
            await this.speechEngine.init();

        } catch (error) {
            console.warn('语音引擎初始化失败:', error);
        }

        // 显示讲解界面
        this.uiController.show();

        // 更新控制按钮状态
        this.uiController.updateControls('playing');

        // 立即更新进度显示
        this.uiController.updateProgress(this.currentStep + 1, this.script.length);


        await this.executeNextStep();
    }



    /**
     * 暂停讲解
     */
    pause() {
        try {
            this.isPaused = true;
            this.isPlaying = false;
            this.speechEngine.pause();
            this.clearCurrentStepTimer();
            this.uiController.updateControls('paused');
            this.uiController.pauseTextAnimation();

        } catch (error) {
            console.error('暂停讲解时出错:', error);
        }
    }



    /**
     * 继续讲解
     */
    resume() {
        if (!this.isPaused) return;

        try {
            this.isPaused = false;
            this.isPlaying = true;
            this.speechEngine.resume();
            this.uiController.updateControls('playing');
            this.uiController.resumeTextAnimation();

        } catch (error) {
            console.error('恢复讲解时出错:', error);
            // 如果语音恢复失败，尝试重新播放当前步骤
            this.executeStep(this.script[this.currentStep]);
        }
    }

    /**
     * 停止讲解
     */
    stop() {
        this.isActive = false;
        this.isPaused = false;
        this.isPlaying = false;
        this.isDestroyed = true;
        this.isProcessing = false;
        this.hasExecutedCurrentStep = false;



        // 强制停止语音
        this.speechEngine.stop();

        // 清除定时器
        this.clearCurrentStepTimer();

        // 停止所有语音合成
        if (window.speechSynthesis) {
            window.speechSynthesis.cancel();
        }

        // 隐藏UI
        this.uiController.hide();
        this.uiController.updateControls('stopped');

        // 清除所有高亮效果
        this.clearAllHighlights();

        // 重新显示启动按钮
        this.showStartButton();


    }

    /**
     * 跳到下一步（跳过语音播放）
     */
    async next() {
        if (!this.isActive) return;

        // 防止过快点击，但允许合理的连续操作
        if (this.isProcessing) {
            return;
        }

        this.isProcessing = true;

        try {
            // 停止当前语音和定时器
            this.speechEngine.stop();
            this.clearCurrentStepTimer();

            // 移动到下一步
            this.currentStep++;
            this.hasExecutedCurrentStep = false;

            // 检查是否超出范围
            if (this.currentStep >= this.script.length) {

                this.complete();
                return;
            }

            // 强制重置字幕动画
            this.uiController.pauseTextAnimation();

            // 执行当前步骤（包括操作和语音）
            const currentStep = this.script[this.currentStep];

            // 获取讲解文本（支持动态文本）
            const displayText = await this.getStepText(currentStep);

            // 更新UI显示
            this.uiController.updateProgress(this.currentStep + 1, this.script.length);
            this.uiController.showText(displayText, true);

            // 执行步骤操作
            if (!this.hasExecutedCurrentStep) {
                await this.executeStep(currentStep);
                this.hasExecutedCurrentStep = true;
            }

            // 播放语音（手动点击时也播放语音）
            if (currentStep.speech !== false) {
                try {
                    await this.speechEngine.speak(displayText, currentStep.speechOptions);
                } catch (error) {
                    console.warn('语音播放失败:', error);
                }
            }
        } finally {
            // 减少延迟，提高响应性
            setTimeout(() => {
                this.isProcessing = false;
            }, 100);
        }
    }

    /**
     * 跳到上一步（跳过语音播放）
     */
    async previous() {
        if (!this.isActive || this.currentStep <= 0) return;

        if (this.isProcessing) {
            return;
        }

        this.isProcessing = true;

        try {
            // 停止当前语音和定时器
            this.speechEngine.stop();
            this.clearCurrentStepTimer();

            // 清除当前步骤的高亮效果
            this.clearAllHighlights();

            // 移动到上一步
            this.currentStep--;
            this.hasExecutedCurrentStep = false;

            // 强制重置字幕动画
            this.uiController.pauseTextAnimation();

            // 执行当前步骤（包括操作和语音）
            const currentStep = this.script[this.currentStep];

            // 获取讲解文本（支持动态文本）
            const displayText = await this.getStepText(currentStep);

            // 更新UI显示
            this.uiController.updateProgress(this.currentStep + 1, this.script.length);
            this.uiController.showText(displayText, true);

            // 执行步骤操作
            if (!this.hasExecutedCurrentStep) {
                await this.executeStep(currentStep);
                this.hasExecutedCurrentStep = true;
            }

            // 播放语音（手动点击时也播放语音）
            if (currentStep.speech !== false) {
                try {
                    await this.speechEngine.speak(displayText, currentStep.speechOptions);
                } catch (error) {
                    console.warn('语音播放失败:', error);
                }
            }
        } finally {
            setTimeout(() => {
                this.isProcessing = false;
            }, 100);
        }
    }

    /**
     * 清除当前步骤的定时器
     */
    clearCurrentStepTimer() {
        if (this.currentStepTimer) {
            clearTimeout(this.currentStepTimer);
            this.currentStepTimer = null;
        }
    }

    /**
     * 跳转到指定步骤
     */
    async goToStep(stepIndex) {
        if (!this.isActive || stepIndex < 0 || stepIndex >= this.script.length) return;

        if (this.isProcessing) {
            return;
        }

        this.isProcessing = true;

        try {
            this.speechEngine.stop();
            this.clearCurrentStepTimer();
            this.clearAllHighlights();

            this.currentStep = stepIndex;
            this.hasExecutedCurrentStep = false;

            await this.executeNextStep();
        } finally {
            setTimeout(() => {
                this.isProcessing = false;
            }, 100);
        }
    }

    /**
     * 执行下一步操作
     */
    async executeNextStep(skipSpeech = false, forceExecute = false) {
        // 如果是强制执行（手动操作），忽略暂停状态
        if (!forceExecute && (this.isPaused || this.isDestroyed)) return;

        if (this.isDestroyed) return;

        if (this.currentStep >= this.script.length) {
            // 讲解完成
            this.complete();
            return;
        }

        const step = this.script[this.currentStep];


        try {


            // 更新进度
            this.uiController.updateProgress(this.currentStep + 1, this.script.length);

            // 获取讲解文本（支持动态文本）
            const displayText = await this.getStepText(step);

            // 显示讲解文本（强制重置字幕滚动）
            this.uiController.showText(displayText, true);

            // 执行操作（如果还没执行过）
            if (!this.hasExecutedCurrentStep) {
                await this.executeStep(step);
                this.hasExecutedCurrentStep = true;
            }

            // 播放语音（除非被跳过或手动跳过）
            if (step.speech !== false && !skipSpeech) {
                try {
                    await this.speechEngine.speak(displayText, step.speechOptions);
                } catch (error) {
                    console.warn('语音播放失败:', error);
                }
            }

            // 如果是自动播放模式（非手动跳过），在语音播放完成后等待1秒再继续
            if (!skipSpeech && !forceExecute && !this.isPaused && !this.isDestroyed) {
                // 语音播放完成后，等待固定的1秒停顿时间，不依赖duration
                const pauseTime = 1000; // 固定1秒停顿

                this.currentStepTimer = setTimeout(() => {
                    if (!this.isPaused && !this.isDestroyed) {
                        this.currentStep++;
                        this.hasExecutedCurrentStep = false;
                        this.executeNextStep();
                    }
                }, pauseTime);
            }

        } catch (error) {
            console.error(`步骤 ${this.currentStep + 1} 执行失败:`, error);
            await this.errorHandler.handle(error, step, this);
        }
    }



    /**
     * 执行具体的操作步骤
     */
    async executeStep(step) {
        switch (step.action) {
            case 'highlight':
                await this.highlightElement(step.target, step.options);
                break;
            case 'click':
                await this.clickElement(step.target, step.options);
                break;
            case 'navigate':
                await this.navigateToPage(step.target, step.options);
                break;
            case 'wait':
                if (step.options && step.options.waitForElement) {
                    try {
                        await this.waitForElement(step.options.waitForElement, step.options.maxWaitTime || 10000);
                    } catch (error) {
                        if (step.options.skipIfNotFound) {
                            console.warn(`⚠️ 跳过未找到的元素: ${step.options.waitForElement}`);
                        } else {
                            throw error;
                        }
                    }
                } else {
                    await this.sleep(step.duration || 1000);
                }
                break;
            case 'scroll':
                await this.scrollToElement(step.target, step.options);
                break;
            case 'input':
                await this.inputText(step.target, step.value, step.options);
                break;
            default:
                console.warn(`未知的操作类型: ${step.action}`);
        }
    }



    /**
     * 查找元素
     */
    findElement(selector, extra = {}) {
        // 获取目标文档（主页面或iframe）
        const targetDoc = this.getTargetDocument();
        if (targetDoc === null) {
            console.log(`🔍 检测到跨域限制，使用扩展查找元素: ${selector}`);
            if (window.intelligentPresentationExtension &&
                typeof window.intelligentPresentationClick === 'function') {
                // 扩展会在其内部处理跨域访问，我们这里只是标记找到了
                // 实际的查找和操作会在点击或高亮时由扩展处理
                return { isExtensionElement: true, selector: selector };
            } else {
                console.warn(`⚠️ 跨域限制且扩展不可用，无法查找元素: ${selector}`);
                return null;
            }
        }

        console.log(`🔍 findElement 查找: ${selector}, 目标文档:`, targetDoc === document ? '主页面' : 'iframe');

        // 1. selector+index
        if (typeof selector === 'string') {
            let elements = Array.from(targetDoc.querySelectorAll(selector));
            // 1. placeholder
            if (extra.placeholder) {
                elements = elements.filter(el => el.placeholder === extra.placeholder);
            }
            // 2. name
            if (extra.name) {
                elements = elements.filter(el => el.name === extra.name);
            }
            // 3. 多属性比对
            if (extra.attrs) {
                elements = elements.filter(el => {
                    for (const k in extra.attrs) {
                        if (el.getAttribute(k) !== extra.attrs[k]) return false;
                    }
                    return true;
                });
            }
            // 4. index
            if (typeof extra.index === 'number' && elements.length > 1) {
                if (elements[extra.index]) return elements[extra.index];
            }
            // 5. 唯一
            if (elements.length === 1) return elements[0];
            // 6. fallback: return first
            if (elements.length > 0) return elements[0];
            // 7. XPath
            if (extra.xpath) {
                try {
                    const result = targetDoc.evaluate(extra.xpath, targetDoc, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
                    if (result && result.singleNodeValue) return result.singleNodeValue;
                } catch (e) {}
            }
            return null;
        } else if (typeof selector === 'object') {
            // 优先尝试使用selector
            if (selector.selector) {
                const element = targetDoc.querySelector(selector.selector);
                if (element) return element;
            }

            // 尝试使用xpath
            if (selector.xpath) {
                try {
                    const result = targetDoc.evaluate(
                        selector.xpath,
                        targetDoc,
                        null,
                        XPathResult.FIRST_ORDERED_NODE_TYPE,
                        null
                    );
                    if (result.singleNodeValue) return result.singleNodeValue;
                } catch (error) {
                    console.warn('XPath查找失败:', error);
                }
            }

            // 尝试根据文本内容查找元素（更精确的匹配）
            if (selector.text) {
                // 首先尝试查找tooltip元素
                const tooltips = targetDoc.querySelectorAll('.leaflet-tooltip');
                for (let tooltip of tooltips) {
                    if (tooltip.textContent && tooltip.textContent.trim() === selector.text) {
                        // 对于tooltip，我们需要找到对应的地图图层来点击
                        const mapContainer = targetDoc.getElementById('map');
                        if (mapContainer) {
                            // 获取iframe中的window对象
                            const targetWindow = this.getTargetWindow();
                            if (targetWindow && targetWindow.lasaLayer) {
                                // 尝试找到对应的图层
                                let targetLayer = null;
                                targetWindow.lasaLayer.eachLayer(layer => {
                                    if (layer.feature && layer.feature.properties &&
                                        layer.feature.properties.name === selector.text) {
                                        targetLayer = layer;
                                    }
                                });
                                if (targetLayer && targetLayer.getElement) {
                                    return targetLayer.getElement();
                                }
                            }
                        }
                        return tooltip;
                    }
                }

                // 然后查找其他包含该文本的元素
                const elements = targetDoc.querySelectorAll('*');
                for (let el of elements) {
                    if (el.textContent && el.textContent.trim() === selector.text) {
                        return el;
                    }
                }

                // 最后尝试模糊匹配
                for (let el of elements) {
                    if (el.textContent && el.textContent.includes(selector.text)) {
                        return el;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 获取目标文档 - 根据运行环境返回正确的document
     */
    getTargetDocument() {
        // 检查是否在主页面环境中
        const isInMainPage = window.self === window.top;

        if (isInMainPage) {
            // 在主页面中，优先尝试访问iframe的document（本项目页面）
            const iframe = document.getElementById('content-frame');
            if (iframe) {
                try {
                    // 尝试访问iframe的contentDocument
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
                    if (iframeDoc) {
                        // 测试是否真的可以访问（避免跨域错误）
                        iframeDoc.querySelector('body'); // 这会在跨域时抛出错误
                        console.log('📄 使用iframe document (本项目页面)');
                        return iframeDoc;
                    }
                } catch (error) {
                    console.log('⚠️ 无法访问iframe内容（跨域限制）:', error.message);
                    // 跨域情况下，返回null表示需要使用扩展
                    return null;
                }
            }
            console.log('📄 使用主页面document');
            return document;
        } else {
            // 在iframe中，直接使用当前document
            console.log('📄 使用当前iframe document');
            return document;
        }
    }

    /**
     * 获取目标窗口 - 根据运行环境返回正确的window
     */
    getTargetWindow() {
        // 检查是否在主页面环境中
        const isInMainPage = window.self === window.top;

        if (isInMainPage) {
            // 在主页面中，需要访问iframe的window
            const iframe = document.getElementById('content-frame');
            if (iframe && iframe.contentWindow) {
                return iframe.contentWindow;
            } else {
                console.warn('⚠️ 无法访问iframe窗口，使用主页面window');
                return window;
            }
        } else {
            // 在iframe中，直接使用当前window
            return window;
        }
    }

    /**
     * 等待元素出现
     */
    async waitForElement(selector, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            const check = () => {
                try {
                    const element = this.findElement(selector);
                    if (element) {
                        console.log(`✅ 元素找到: ${selector}`);
                        resolve(element);
                        return;
                    }

                    if (Date.now() - startTime > timeout) {
                        console.warn(`⏰ 等待元素超时: ${selector}`);
                        reject(new Error(`等待元素超时: ${selector}`));
                        return;
                    }

                    setTimeout(check, 100);
                } catch (error) {
                    console.error(`❌ waitForElement 检查过程中出错:`, error);
                    if (Date.now() - startTime > timeout) {
                        reject(error);
                    } else {
                        setTimeout(check, 100);
                    }
                }
            };

            check();
        });
    }

    /**
     * 清除所有高亮效果
     */
    clearAllHighlights() {
        document.querySelectorAll('.presentation-highlight-overlay, .presentation-highlight').forEach(el => {
            el.remove();
        });
    }

    /**
     * 工具函数：延时
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 讲解完成
     */
    complete() {
        this.uiController.showText('讲解完成！感谢您的观看。');
        this.uiController.updateControls('completed');

        setTimeout(() => {
            this.stop();
        }, 3000);
    }

    /**
     * 显示启动按钮
     */
    showStartButton() {
        const startButton = document.getElementById('presentation-start-btn');
        if (startButton) {
            startButton.style.display = 'flex';
        }
    }

    /**
     * 绑定控制事件
     */
    bindEvents() {
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (!this.isActive) return;
            
            switch (e.key) {
                case ' ':
                case 'Escape':
                    e.preventDefault();
                    this.isPaused ? this.resume() : this.pause();
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    this.next();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    this.previous();
                    break;
            }
        });
    }

    /**
     * 加载讲解脚本
     */
    async loadScript() {
        // 等待脚本加载
        let retryCount = 0;
        const maxRetries = 10;

        while (retryCount < maxRetries) {
            if (window.presentationScript && window.presentationScript.length > 0) {
                return window.presentationScript;
            }

            await new Promise(resolve => setTimeout(resolve, 100));
            retryCount++;
        }

        console.error('脚本加载失败或为空');
        return [];
    }

    /**
     * 获取步骤的显示文本（支持动态文本）
     */
    async getStepText(step) {
        try {
            // 如果没有动态文本配置，返回静态文本
            if (!step.dynamicText) {
                console.log('📝 使用静态文本:', step.text);
                return step.text || '';
            }

            console.log('🔍 获取动态文本:', step.dynamicText);

            // 获取动态文本内容
            const dynamicContent = await this.getDynamicText(step.dynamicText);

            console.log('✅ 动态文本获取结果:', dynamicContent);
            console.log('📄 静态文本兜底:', step.text);
            console.log('🔄 fallback文本:', step.dynamicText.fallback);

            // 如果获取成功，返回动态文本；否则返回静态文本作为兜底
            const finalText = dynamicContent || step.text || step.dynamicText.fallback || '';
            console.log('🎯 最终显示文本:', finalText);

            return finalText;

        } catch (error) {
            console.warn('动态文本获取失败，使用静态文本:', error);
            return step.text || step.dynamicText?.fallback || '';
        }
    }

    /**
     * 获取动态文本内容
     */
    async getDynamicText(config) {
        const { source, selector, property, template, fallback } = config;

        try {
            console.log(`🔄 开始获取动态文本，类型: ${source}`);
            let content = '';

            switch (source) {
                case 'element':
                    content = await this.getElementText(selector, property);
                    console.log(`📄 element类型获取结果: ${content}`);
                    break;
                case 'attribute':
                    content = await this.getElementAttribute(selector, property);
                    console.log(`🏷️ attribute类型获取结果: ${content}`);
                    break;
                case 'computed':
                    content = await this.getComputedValue(selector, property);
                    console.log(`⚙️ computed类型获取结果: ${content}`);
                    break;
                case 'multiple':
                    content = await this.getMultipleElementsText(config);
                    console.log(`🔢 multiple类型获取结果: ${content}`);
                    break;
                default:
                    console.warn('未知的动态文本源类型:', source);
                    return fallback || '';
            }

            // 应用模板（multiple类型已经在getMultipleElementsText中应用了模板）
            if (source !== 'multiple' && template && content !== null && content !== undefined) {
                const templatedContent = template.replace('{content}', content);
                console.log(`🎨 应用模板后: ${templatedContent}`);
                return templatedContent;
            }

            console.log(`✅ 最终动态文本内容: ${content}`);
            return content || fallback || '';

        } catch (error) {
            console.warn('动态文本获取失败:', error);
            return fallback || '';
        }
    }

    /**
     * 获取元素文本内容
     */
    async getElementText(selector, property = 'textContent') {
        try {
            console.log(`🔍 获取元素文本: ${selector}, 属性: ${property}`);

            // 首先尝试在主页面查找
            const element = document.querySelector(selector);
            if (element) {
                const text = this.extractTextFromElement(element, property);
                console.log('✅ 主页面找到元素，文本:', text);
                return text;
            }

            // 如果主页面没找到，尝试在iframe中查找
            const iframeText = await this.getTextFromIframes(selector, property);
            if (iframeText !== null) {
                console.log('✅ iframe中找到元素，文本:', iframeText);
                return iframeText;
            }

            console.warn('❌ 未找到元素:', selector);
            return null;

        } catch (error) {
            console.error('获取元素文本失败:', error);
            return null;
        }
    }

    /**
     * 从元素中提取文本
     */
    extractTextFromElement(element, property) {
        switch (property) {
            case 'textContent':
                return element.textContent?.trim() || '';
            case 'innerText':
                return element.innerText?.trim() || '';
            case 'innerHTML':
                return element.innerHTML?.trim() || '';
            case 'value':
                return element.value || '';
            default:
                // 尝试作为属性获取
                return element.getAttribute(property) || element[property] || '';
        }
    }

    /**
     * 从iframe中获取文本
     */
    async getTextFromIframes(selector, property) {
        const iframes = document.querySelectorAll('iframe');
        console.log(`🖼️ 在 ${iframes.length} 个iframe中查找元素`);

        for (let i = 0; i < iframes.length; i++) {
            const iframe = iframes[i];
            try {
                // 尝试直接访问iframe内容（同域）
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                const element = iframeDoc.querySelector(selector);

                if (element) {
                    const text = this.extractTextFromElement(element, property);
                    console.log(`✅ iframe[${i}] 直接访问成功，文本:`, text);
                    return text;
                }
            } catch (error) {
                // 跨域限制，使用消息传递
                console.log(`📄 iframe[${i}] 跨域限制，尝试消息传递:`, iframe.src);

                try {
                    const text = await this.getTextFromIframeMessage(iframe, selector, property);
                    if (text !== null) {
                        console.log(`✅ iframe[${i}] 消息传递成功，文本:`, text);
                        return text;
                    }
                } catch (msgError) {
                    console.log(`❌ iframe[${i}] 消息传递失败:`, msgError.message);
                }
            }
        }

        return null;
    }

    /**
     * 通过消息传递从iframe获取文本
     */
    async getTextFromIframeMessage(iframe, selector, property) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('获取文本超时'));
            }, 3000);

            const messageHandler = (event) => {
                if (event.data?.type === 'DYNAMIC_TEXT_RESPONSE' &&
                    event.data?.selector === selector) {
                    clearTimeout(timeout);
                    window.removeEventListener('message', messageHandler);

                    if (event.data.success) {
                        resolve(event.data.content);
                    } else {
                        reject(new Error(event.data.error || '获取文本失败'));
                    }
                }
            };

            window.addEventListener('message', messageHandler);

            // 发送获取文本的消息
            iframe.contentWindow.postMessage({
                type: 'GET_DYNAMIC_TEXT',
                selector: selector,
                property: property
            }, '*');
        });
    }

    /**
     * 获取元素属性值
     */
    async getElementAttribute(selector, attributeName) {
        return await this.getElementText(selector, attributeName);
    }

    /**
     * 获取计算值
     */
    async getComputedValue(selector, computeType) {
        try {
            console.log(`🔢 获取计算值: ${selector}, 类型: ${computeType}`);

            switch (computeType) {
                case 'count':
                    return await this.getElementCount(selector);
                case 'visible':
                    const element = await this.findElement(selector);
                    return element && element.offsetParent !== null ? '可见' : '隐藏';
                case 'timestamp':
                    return new Date().toLocaleString('zh-CN');
                case 'url':
                    return window.location.href;
                case 'domain':
                    return window.location.hostname;
                default:
                    console.warn('未知的计算类型:', computeType);
                    return '';
            }
        } catch (error) {
            console.error('计算值获取失败:', error);
            return '';
        }
    }

    /**
     * 获取元素数量（支持跨域）
     */
    async getElementCount(selector) {
        try {
            console.log(`🔍 统计元素数量: ${selector}`);

            // 首先尝试在主页面查找
            const mainElements = document.querySelectorAll(selector);
            console.log(`主页面中找到 ${mainElements.length} 个元素: ${selector}`);

            if (mainElements.length > 0) {
                return mainElements.length.toString();
            }

            // 如果主页面没找到，尝试在iframe中查找
            const iframeCount = await this.getCountFromIframes(selector);
            if (iframeCount !== null) {
                console.log(`✅ iframe中找到 ${iframeCount} 个元素`);
                return iframeCount.toString();
            }

            console.warn('❌ 未找到任何元素:', selector);
            return '0';

        } catch (error) {
            console.error('元素数量统计失败:', error);
            return '0';
        }
    }

    /**
     * 从iframe中获取元素数量
     */
    async getCountFromIframes(selector) {
        const iframes = document.querySelectorAll('iframe');
        console.log(`🖼️ 在 ${iframes.length} 个iframe中统计元素数量`);

        for (let i = 0; i < iframes.length; i++) {
            const iframe = iframes[i];
            try {
                // 尝试直接访问iframe内容（同域）
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                const elements = iframeDoc.querySelectorAll(selector);

                if (elements.length > 0) {
                    console.log(`✅ iframe[${i}] 直接访问成功，找到 ${elements.length} 个元素`);
                    return elements.length;
                }
            } catch (error) {
                // 跨域限制，使用消息传递
                console.log(`📄 iframe[${i}] 跨域限制，尝试消息传递:`, iframe.src);

                try {
                    const count = await this.getCountFromIframeMessage(iframe, selector);
                    if (count !== null) {
                        console.log(`✅ iframe[${i}] 消息传递成功，找到 ${count} 个元素`);
                        return count;
                    }
                } catch (msgError) {
                    console.log(`❌ iframe[${i}] 消息传递失败:`, msgError.message);
                }
            }
        }

        return null;
    }

    /**
     * 通过消息传递从iframe获取元素数量
     */
    async getCountFromIframeMessage(iframe, selector) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('获取元素数量超时'));
            }, 3000);

            const messageHandler = (event) => {
                if (event.data?.type === 'ELEMENT_COUNT_RESPONSE' &&
                    event.data?.selector === selector) {
                    clearTimeout(timeout);
                    window.removeEventListener('message', messageHandler);

                    if (event.data.success) {
                        resolve(event.data.count);
                    } else {
                        reject(new Error(event.data.error || '获取元素数量失败'));
                    }
                }
            };

            window.addEventListener('message', messageHandler);

            // 发送获取元素数量的消息
            iframe.contentWindow.postMessage({
                type: 'GET_ELEMENT_COUNT',
                selector: selector
            }, '*');
        });
    }

    /**
     * 查找单个元素（支持跨域）
     */
    async findElement(selector) {
        // 首先尝试在主页面查找
        const element = document.querySelector(selector);
        if (element) {
            return element;
        }

        // 如果主页面没找到，尝试在iframe中查找
        const iframes = document.querySelectorAll('iframe');
        for (let i = 0; i < iframes.length; i++) {
            const iframe = iframes[i];
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                const iframeElement = iframeDoc.querySelector(selector);
                if (iframeElement) {
                    return iframeElement;
                }
            } catch (error) {
                // 跨域限制，无法直接访问
                continue;
            }
        }

        return null;
    }

    /**
     * 获取多个元素的文本（支持混合类型）
     */
    async getMultipleElementsText(config) {
        try {
            const { selectors, template } = config;
            const contents = [];

            console.log('🔄 开始获取多个元素内容:', selectors);

            for (let i = 0; i < selectors.length; i++) {
                const { selector, property = 'textContent' } = selectors[i];
                let content = '';

                console.log(`📝 获取第${i}个元素: ${selector}, 属性: ${property}`);

                // 根据属性类型选择获取方式
                if (property === 'count') {
                    // 计算值：元素数量
                    content = await this.getElementCount(selector);
                    console.log(`🔢 元素数量: ${content}`);
                } else if (property === 'visible') {
                    // 计算值：可见性
                    content = await this.getComputedValue(selector, 'visible');
                    console.log(`👁️ 可见性: ${content}`);
                } else if (property === 'timestamp' || property === 'url' || property === 'domain') {
                    // 其他计算值
                    content = await this.getComputedValue(selector, property);
                    console.log(`⚙️ 计算值 ${property}: ${content}`);
                } else {
                    // 文本内容或属性值
                    content = await this.getElementText(selector, property);
                    console.log(`📄 文本内容: ${content}`);
                }

                contents.push(content || '');
            }

            console.log('✅ 所有元素内容获取完成:', contents);

            if (template) {
                let result = template;
                contents.forEach((content, index) => {
                    result = result.replace(`{${index}}`, content);
                });
                console.log('🎯 应用模板后的结果:', result);
                return result;
            }

            return contents.join(' ');

        } catch (error) {
            console.error('多元素文本获取失败:', error);
            return '';
        }
    }

    /**
     * 输入文本到目标元素（支持跨域和传统）
     */
    async inputText(target, value, options = {}) {
        // 1. 优先使用扩展（跨域）
        if (this.extensionAvailable && window.intelligentPresentationExtensionBridge) {
            try {
                await window.intelligentPresentationExtensionBridge.input(target, value, options);
                return;
            } catch (e) {
                console.warn('扩展输入失败，降级为传统方式:', e);
            }
        }
        // 2. 传统方式
        try {
            // 支持传递辅助信息
            const extra = {
                placeholder: options && options.placeholder,
                index: options && typeof options.index === 'number' ? options.index : undefined,
                xpath: options && options.xpath
            };
            const element = this.findElement(target, extra);
            if (!element) throw new Error('未找到输入目标元素: ' + target);
            
            // 检查是否是扩展元素（跨域情况）
            if (element && typeof element === 'object' && element.isExtensionElement) {
                console.log('检测到跨域元素，使用扩展输入');
                if (window.intelligentPresentationExtensionBridge) {
                    await window.intelligentPresentationExtensionBridge.input(target, value, options);
                    return;
                } else {
                    throw new Error('跨域元素但扩展不可用');
                }
            }
            
            // 确保element是真正的DOM元素
            if (!element || typeof element.focus !== 'function') {
                throw new Error('找到的元素不是有效的DOM元素: ' + target);
            }
            
            element.focus();
            if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                element.value = '';
            }
            element.value = value;
            const event = new Event('input', { bubbles: true });
            element.dispatchEvent(event);
            const changeEvent = new Event('change', { bubbles: true });
            element.dispatchEvent(changeEvent);
        } catch (err) {
            console.error('inputText 执行失败:', err);
            throw err;
        }
    }
}

// 暴露到全局作用域
window.PresentationController = PresentationController;
