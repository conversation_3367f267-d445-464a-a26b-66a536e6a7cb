<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能讲解助手</title>
    <style>
        body {
            width: 300px;
            min-height: 200px;
            margin: 0;
            padding: 16px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            background: #f8f9fa;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .logo {
            width: 48px;
            height: 48px;
            margin: 0 auto 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        
        .title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }
        
        .subtitle {
            font-size: 12px;
            color: #666;
            margin: 4px 0 0 0;
        }
        
        .status {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status.active {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.inactive {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
        }
        
        .status.active .status-icon {
            background: #28a745;
        }
        
        .status.inactive .status-icon {
            background: #dc3545;
        }
        
        .actions {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-align: center;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .info {
            font-size: 12px;
            color: #666;
            text-align: center;
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid #dee2e6;
        }
        
        .version {
            font-size: 11px;
            color: #999;
            text-align: center;
            margin-top: 8px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🎯</div>
        <h1 class="title">智能讲解助手</h1>
        <p class="subtitle">跨域访问支持插件</p>
    </div>
    
    <div id="status" class="status">
        <div class="status-icon"></div>
        <span id="status-text">检查状态中...</span>
    </div>
    
    <div class="actions">
        <button id="test-btn" class="btn btn-primary">测试功能</button>
        <button id="clear-btn" class="btn btn-secondary">清除高亮</button>
        <a href="#" id="help-btn" class="btn btn-secondary">使用帮助</a>
    </div>
    
    <div class="info">
        <p>此插件为智能讲解系统提供跨域访问支持，允许在iframe中操作第三方页面元素。</p>
    </div>
    
    <div class="version">
        版本 1.0.0
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
