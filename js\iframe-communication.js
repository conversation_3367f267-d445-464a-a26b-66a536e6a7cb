/**
 * iframe通信接口 - 处理与主页面的通信
 */
class IframeCommunication {
    constructor() {
        this.isInIframe = window.self !== window.top;
        this.parentOrigin = null;
        this.initialized = false;
        
        // 绑定方法
        this.handleMessage = this.handleMessage.bind(this);
        
        // 初始化
        this.init();
    }
    
    /**
     * 初始化通信接口
     */
    init() {
        if (!this.isInIframe) {
            console.log('📱 当前页面不在iframe中，跳过通信初始化');
            return;
        }
        
        console.log('📡 初始化iframe通信接口...');
        
        // 监听来自主页面的消息
        window.addEventListener('message', this.handleMessage);

        // 添加快捷键监听
        this.addKeyboardShortcuts();

        // 拦截页面导航
        this.interceptNavigation();
        
        // 向主页面发送准备就绪消息
        this.sendToParent('IFRAME_READY', {
            page: this.getCurrentPage(),
            url: window.location.href
        });
    }
    
    /**
     * 处理来自主页面的消息
     */
    handleMessage(event) {
        if (!event.data || !event.data.type) return;
        
        console.log('📨 iframe收到消息:', event.data.type);
        
        switch (event.data.type) {
            case 'MAIN_INIT':
                // 主页面初始化完成
                this.parentOrigin = event.data.data.parentOrigin;
                this.initialized = true;
                console.log('✅ 与主页面通信建立成功');
                break;
                
            case 'VOICE_COMMAND':
                // 处理语音命令
                this.handleVoiceCommand(event.data.command);
                break;
                
            case 'PRESENTATION_COMMAND':
                // 处理讲解命令
                this.handlePresentationCommand(event.data.command);
                break;

            case 'PAGE_CHANGED':
                // 处理页面变化通知
                this.handlePageChanged(event.data.data);
                break;

            case 'GET_DYNAMIC_TEXT':
                // 处理动态文本获取请求
                this.handleGetDynamicText(event.data);
                break;

            case 'GET_ELEMENT_COUNT':
                // 处理元素数量获取请求
                this.handleGetElementCount(event.data);
                break;

            default:
                console.log('⚠️ iframe未处理的消息类型:', event.data.type);
        }
    }
    
    /**
     * 拦截页面导航
     */
    interceptNavigation() {
        // 拦截所有链接点击
        document.addEventListener('click', (event) => {
            const link = event.target.closest('a');
            if (link && link.href) {
                const url = new URL(link.href);
                const path = url.pathname.split('/').pop();

                // 如果是内部页面导航，拦截并通知主页面
                if (path === 'index.html' || path === 'realtime.html') {
                    event.preventDefault();
                    event.stopPropagation();
                    console.log('🔗 拦截链接导航:', path);
                    this.navigateTo(path);
                }
            }
        });

        // 拦截返回首页按钮和其他导航按钮
        this.interceptNavigationButtons();

        // 监听页面加载完成后的动态按钮
        document.addEventListener('DOMContentLoaded', () => {
            this.interceptNavigationButtons();
        });

        // 使用MutationObserver监听动态添加的导航元素
        this.observeNavigationElements();
    }

    /**
     * 拦截导航按钮
     */
    interceptNavigationButtons() {
        // 1. 特殊处理返回首页按钮
        this.handleHomeButtons();

        // 2. 处理其他导航按钮
        const navigationSelectors = [
            '[onclick*="goHome"]',
            '.back-btn',
            '[href="index.html"]',
            '[href="realtime.html"]',
            '[onclick*="index.html"]',
            '[onclick*="realtime.html"]'
        ];

        navigationSelectors.forEach(selector => {
            const buttons = document.querySelectorAll(selector);
            buttons.forEach(button => {
                // 跳过返回首页按钮
                if (button.classList.contains('home-btn')) {
                    return;
                }

                // 移除原有的onclick事件
                button.removeAttribute('onclick');

                // 添加新的点击事件
                button.addEventListener('click', (event) => {
                    event.preventDefault();
                    event.stopPropagation();

                    // 根据按钮类型确定目标页面
                    let targetPage = 'index.html';
                    if (button.href && button.href.includes('realtime.html')) {
                        targetPage = 'realtime.html';
                    } else if (button.onclick && button.onclick.toString().includes('realtime.html')) {
                        targetPage = 'realtime.html';
                    }

                    console.log('🔘 拦截按钮导航:', targetPage);
                    this.navigateTo(targetPage);
                });
            });
        });
    }

    /**
     * 处理返回首页按钮
     */
    handleHomeButtons() {
        const homeButtons = document.querySelectorAll('.home-btn');
        homeButtons.forEach(button => {
            const onclickAttr = button.getAttribute('onclick');
            if (onclickAttr && onclickAttr.includes('goToHomePage')) {
                // 检查API配置
                if (window.API_CONFIG && window.API_CONFIG.homePageMode && window.API_CONFIG.homePageMode.includeInIframe) {
                    // 包含模式：拦截并处理
                    console.log('🏠 包含模式：拦截返回首页按钮');
                    button.removeAttribute('onclick');

                    // 移除可能存在的重复事件监听器
                    const newButton = button.cloneNode(true);
                    button.parentNode.replaceChild(newButton, button);

                    newButton.addEventListener('click', (event) => {
                        event.preventDefault();
                        event.stopPropagation();
                        this.handleHomeButtonClick();
                    });
                } else {
                    // 直接模式：保留原始功能
                    console.log('🏠 直接模式：保留返回首页按钮的原始功能');
                }
            }
        });
    }

    /**
     * 处理返回首页按钮点击
     */
    handleHomeButtonClick() {
        console.log('🏠 处理返回首页按钮点击');

        if (window.API_CONFIG && window.API_CONFIG.baseUrl) {
            this.sendToParent('NAVIGATE_TO_BASE_URL', {
                url: window.API_CONFIG.baseUrl,
                mode: 'include'
            });
        } else {
            console.error('❌ API_CONFIG.baseUrl 未定义');
        }
    }

    /**
     * 观察动态添加的导航元素
     */
    observeNavigationElements() {
        // 等待DOM加载完成后再设置观察器
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupMutationObserver();
            });
        } else {
            this.setupMutationObserver();
        }
    }

    /**
     * 设置MutationObserver
     */
    setupMutationObserver() {
        if (!document.body) {
            // 如果body还没有加载，稍后再试
            setTimeout(() => this.setupMutationObserver(), 100);
            return;
        }

        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // 检查新添加的元素是否包含导航链接
                            const links = node.querySelectorAll ? node.querySelectorAll('a[href*=".html"]') : [];
                            links.forEach(link => {
                                try {
                                    const path = new URL(link.href, window.location.origin).pathname.split('/').pop();
                                    if (path === 'index.html' || path === 'realtime.html') {
                                        link.addEventListener('click', (event) => {
                                            event.preventDefault();
                                            this.navigateTo(path);
                                        });
                                    }
                                } catch (error) {
                                    console.warn('⚠️ 处理动态链接时出错:', error);
                                }
                            });
                        }
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        console.log('👀 导航元素观察器已启动');
    }
    
    /**
     * 导航到指定页面
     */
    navigateTo(page) {
        if (!this.isInIframe) {
            // 如果不在iframe中，直接跳转
            window.location.href = page;
            return;
        }

        console.log('🔄 请求导航到:', page);
        this.sendToParent('NAVIGATE', { page });
    }
    
    /**
     * 处理语音命令
     */
    handleVoiceCommand(command) {
        console.log('🎤 处理语音命令:', command);
        
        // 根据当前页面和命令类型处理
        switch (command) {
            case 'start_presentation':
                if (window.PresentationSystem && window.PresentationSystem.start) {
                    window.PresentationSystem.start();
                }
                break;
                
            case 'pause_presentation':
                if (window.presentationController && window.presentationController.pause) {
                    window.presentationController.pause();
                }
                break;
                
            case 'resume_presentation':
                if (window.presentationController && window.presentationController.resume) {
                    window.presentationController.resume();
                }
                break;
                
            case 'stop_presentation':
                if (window.presentationController && window.presentationController.stop) {
                    window.presentationController.stop();
                }
                break;
                
            default:
                console.log('⚠️ 未处理的语音命令:', command);
        }
    }
    
    /**
     * 处理讲解命令
     */
    handlePresentationCommand(command) {
        console.log('🎯 处理讲解命令:', command);
        // 可以在这里添加更多讲解相关的命令处理
    }

    /**
     * 处理动态文本获取请求
     */
    handleGetDynamicText(data) {
        console.log('📝 处理动态文本获取请求:', data);

        try {
            const { selector, property = 'textContent' } = data;

            // 查找元素
            const element = document.querySelector(selector);

            if (element) {
                // 提取文本内容
                let content = '';
                switch (property) {
                    case 'textContent':
                        content = element.textContent?.trim() || '';
                        break;
                    case 'innerText':
                        content = element.innerText?.trim() || '';
                        break;
                    case 'innerHTML':
                        content = element.innerHTML?.trim() || '';
                        break;
                    case 'value':
                        content = element.value || '';
                        break;
                    default:
                        // 尝试作为属性获取
                        content = element.getAttribute(property) || element[property] || '';
                }

                console.log('✅ 成功获取文本:', content);

                // 发送响应消息
                window.parent.postMessage({
                    type: 'DYNAMIC_TEXT_RESPONSE',
                    selector: selector,
                    success: true,
                    content: content
                }, '*');

            } else {
                console.log('❌ 未找到元素:', selector);

                // 发送失败响应
                window.parent.postMessage({
                    type: 'DYNAMIC_TEXT_RESPONSE',
                    selector: selector,
                    success: false,
                    error: '未找到元素: ' + selector
                }, '*');
            }

        } catch (error) {
            console.error('❌ 动态文本获取失败:', error);

            // 发送错误响应
            window.parent.postMessage({
                type: 'DYNAMIC_TEXT_RESPONSE',
                selector: data.selector,
                success: false,
                error: error.message
            }, '*');
        }
    }

    /**
     * 处理元素数量获取请求
     */
    handleGetElementCount(data) {
        console.log('🔢 处理元素数量获取请求:', data);

        try {
            const { selector } = data;

            // 查找所有匹配的元素
            const elements = document.querySelectorAll(selector);
            const count = elements.length;

            console.log(`✅ 成功统计元素数量: ${count} 个 (${selector})`);

            // 发送响应消息
            window.parent.postMessage({
                type: 'ELEMENT_COUNT_RESPONSE',
                selector: selector,
                success: true,
                count: count
            }, '*');

        } catch (error) {
            console.error('❌ 元素数量获取失败:', error);

            // 发送错误响应
            window.parent.postMessage({
                type: 'ELEMENT_COUNT_RESPONSE',
                selector: data.selector,
                success: false,
                error: error.message,
                count: 0
            }, '*');
        }
    }
    
    /**
     * 向主页面发送消息
     */
    sendToParent(type, data = {}) {
        if (!this.isInIframe) return;
        
        try {
            window.parent.postMessage({
                type,
                data,
                source: 'iframe',
                page: this.getCurrentPage()
            }, '*');
        } catch (error) {
            console.error('❌ 发送消息到主页面失败:', error);
        }
    }
    
    /**
     * 获取当前页面名称
     */
    getCurrentPage() {
        const path = window.location.pathname;
        return path.split('/').pop() || 'index.html';
    }
    
    /**
     * 通知主页面语音控制状态变化
     */
    notifyVoiceControlStatus(enabled) {
        this.sendToParent('VOICE_CONTROL_STATUS', { enabled });
    }
    
    /**
     * 通知主页面讲解状态变化
     */
    notifyPresentationStatus(active) {
        this.sendToParent('PRESENTATION_STATUS', { active });
    }

    /**
     * 处理页面变化通知
     */
    handlePageChanged(data) {
        console.log('📄 页面变化通知:', data);

        // 可以在这里添加页面特定的初始化逻辑
        if (data.page === 'realtime.html') {
            // 实时监控页面特定初始化
            this.initRealtimePage();
        } else if (data.page === 'index.html') {
            // 点位分布页面特定初始化
            this.initIndexPage();
        }
    }

    /**
     * 初始化实时监控页面
     */
    initRealtimePage() {
        console.log('🔄 初始化实时监控页面...');
        // 可以在这里添加实时监控页面的特定初始化逻辑
    }

    /**
     * 初始化点位分布页面
     */
    initIndexPage() {
        console.log('🔄 初始化点位分布页面...');
        // 可以在这里添加点位分布页面的特定初始化逻辑
    }

    /**
     * 添加快捷键监听
     */
    addKeyboardShortcuts() {
        // 检测浏览器类型
        const isEdge = navigator.userAgent.includes('Edg');
        console.log('🎹 浏览器检测:', isEdge ? 'Edge' : 'Other');

        // 为Edge添加额外的事件监听器
        if (isEdge) {
            this.addEdgeCompatibilityFix();
        }

        document.addEventListener('keydown', (event) => {
            // Ctrl + Shift + P: 启动/停止智能讲解
            if (event.ctrlKey && event.shiftKey && event.key === 'P') {
                event.preventDefault();
                event.stopPropagation();
                console.log('🎹 iframe快捷键: Ctrl+Shift+P');
                this.sendToParent('SHORTCUT_KEY', { key: 'presentation_toggle' });
            }

            // Ctrl + Shift + Space: 暂停/恢复讲解
            if (event.ctrlKey && event.shiftKey && event.code === 'Space') {
                event.preventDefault();
                event.stopPropagation();
                console.log('🎹 iframe快捷键: Ctrl+Shift+Space');
                this.sendToParent('SHORTCUT_KEY', { key: 'presentation_pause_resume' });
            }

            // Ctrl + 空格: 启用/禁用语音控制
            // 使用多种检测方式确保兼容性
            const isCtrlSpace = event.ctrlKey &&
                               (event.key === ' ' ||
                                event.code === 'Space' ||
                                event.keyCode === 32);

            if (isCtrlSpace) {
                event.preventDefault();
                event.stopPropagation();
                event.stopImmediatePropagation();

                console.log('🎹 iframe快捷键: Ctrl+空格');
                console.log('🎹 iframe事件详情:', {
                    key: event.key,
                    code: event.code,
                    keyCode: event.keyCode,
                    ctrlKey: event.ctrlKey,
                    browser: navigator.userAgent.includes('Edg') ? 'Edge' : 'Other'
                });

                this.sendToParent('SHORTCUT_KEY', { key: 'voice_toggle' });
            }
        });

        console.log('🎹 iframe快捷键监听已启用:');
        console.log('🎹   Ctrl+Shift+P: 启动/停止智能讲解');
        console.log('🎹   Ctrl+Shift+Space: 暂停/恢复讲解');
        console.log('🎹   Ctrl+空格: 启用/禁用语音控制');
    }

    /**
     * Edge浏览器兼容性修复
     */
    addEdgeCompatibilityFix() {
        console.log('🔧 启用Edge浏览器兼容性修复');

        // 添加额外的keyup事件监听（Edge有时在keydown中不触发）
        document.addEventListener('keyup', (event) => {
            const isCtrlSpace = event.ctrlKey &&
                               (event.key === ' ' ||
                                event.code === 'Space' ||
                                event.keyCode === 32);

            if (isCtrlSpace) {
                console.log('🔧 Edge兼容性: 在keyup中检测到Ctrl+空格');
                event.preventDefault();
                event.stopPropagation();
                this.sendToParent('SHORTCUT_KEY', { key: 'voice_toggle' });
            }
        }, { capture: true });

        // 添加额外的全局监听器
        window.addEventListener('keydown', (event) => {
            const isCtrlSpace = event.ctrlKey &&
                               (event.key === ' ' ||
                                event.code === 'Space' ||
                                event.keyCode === 32);

            if (isCtrlSpace) {
                console.log('🔧 Edge兼容性: 在window级别检测到Ctrl+空格');
                event.preventDefault();
                event.stopPropagation();
                this.sendToParent('SHORTCUT_KEY', { key: 'voice_toggle' });
            }
        }, { capture: true });

        console.log('🔧 Edge兼容性修复已启用');
    }
}

// 全局函数：返回首页（兼容现有代码）
function goHome() {
    if (window.iframeCommunication) {
        window.iframeCommunication.navigateTo('index.html');
    } else {
        window.location.href = 'index.html';
    }
}

// 初始化通信接口
window.iframeCommunication = new IframeCommunication();

// 导出到全局作用域供其他脚本使用
window.IframeCommunication = IframeCommunication;
