/**
 * 智能讲解助手 - 简化内容脚本
 */

console.log('🎯 智能讲解助手内容脚本加载:', window.location.href);

// 使用消息传递的方式与页面通信，避免CSP限制
window.intelligentPresentationExtension = true;

// 定义高亮功能函数
window.intelligentPresentationHighlight = function(selector, color = '#ffeb3b', duration = 3000) {
    try {
        console.log('🎯 高亮元素:', selector, '颜色:', color, '持续时间:', duration + 'ms');

        // 在当前文档中查找元素
        let elements = document.querySelectorAll(selector);
        console.log('当前文档中找到', elements.length, '个元素:', selector);

        // 如果在主页面且没找到元素，通过消息传递到所有frame
        if (elements.length === 0 && window.self === window.top) {
            console.log('主页面中未找到元素，向所有frame发送高亮消息');

            // 向所有iframe发送消息
            const iframes = document.querySelectorAll('iframe');
            iframes.forEach((iframe, index) => {
                try {
                    iframe.contentWindow.postMessage({
                        type: 'INTELLIGENT_PRESENTATION_HIGHLIGHT',
                        selector: selector,
                        color: color,
                        duration: duration
                    }, '*');
                    console.log('向iframe[' + index + ']发送高亮消息');
                } catch (e) {
                    console.log('无法向iframe[' + index + ']发送消息:', e.message);
                }
            });

            // 返回成功，因为消息已发送
            return { success: true, count: 0, messagesSent: iframes.length };
        }

        if (elements.length === 0) {
            throw new Error('未找到元素: ' + selector);
        }

        console.log('总共找到', elements.length, '个元素进行高亮');

        // 高亮所有找到的元素 - 使用与系统自带高亮完全相同的样式
        elements.forEach(element => {
            // 直接设置样式，不使用CSS类，与系统自带高亮保持一致
            element.style.outline = `3px solid ${color}`;
            element.style.outlineOffset = '2px';
            element.style.transition = 'all 0.3s ease';

            // 如果设置了持续时间，自动移除高亮
            if (duration > 0) {
                setTimeout(() => {
                    element.style.outline = '';
                    element.style.outlineOffset = '';
                    element.style.transition = '';
                }, duration);
            }
        });

        return { success: true, count: elements.length };
    } catch (error) {
        console.error('高亮失败:', error);
        return { success: false, error: error.message };
    }
};

// 定义点击功能函数
window.intelligentPresentationClick = function(selector, showIndicator = true) {
    try {
        console.log('🎯 点击元素:', selector);

        // 在当前文档中查找元素
        let element = document.querySelector(selector);
        console.log('在当前文档中查找元素:', selector);

        // 如果在主页面且没找到元素，通过消息传递到所有frame
        if (!element && window.self === window.top) {
            console.log('主页面中未找到元素，向所有frame发送点击消息');

            // 向所有iframe发送消息
            const iframes = document.querySelectorAll('iframe');
            iframes.forEach((iframe, index) => {
                try {
                    iframe.contentWindow.postMessage({
                        type: 'INTELLIGENT_PRESENTATION_CLICK',
                        selector: selector,
                        showIndicator: showIndicator
                    }, '*');
                    console.log('向iframe[' + index + ']发送点击消息');
                } catch (e) {
                    console.log('无法向iframe[' + index + ']发送消息:', e.message);
                }
            });

            // 返回成功，因为消息已发送
            return { success: true, messagesSent: iframes.length };
        }

        if (!element) {
            throw new Error('未找到元素: ' + selector);
        }

        console.log('点击元素:', selector);

        if (showIndicator) {
            element.classList.add('intelligent-presentation-click-indicator');
            setTimeout(() => {
                element.classList.remove('intelligent-presentation-click-indicator');
            }, 600);
        }

        // 模拟点击
        element.click();

        return { success: true };
    } catch (error) {
        console.error('点击失败:', error);
        return { success: false, error: error.message };
    }
};

// 定义清除高亮功能函数
window.intelligentPresentationClearHighlight = function() {
    try {
        let totalCount = 0;

        // 清除当前文档中的高亮 - 清除所有可能的高亮样式
        const allElements = document.querySelectorAll('*');
        allElements.forEach(element => {
            // 清除扩展设置的高亮样式
            if (element.style.outline && element.style.outline.includes('3px solid')) {
                element.style.outline = '';
                element.style.outlineOffset = '';
                element.style.transition = '';
            }
        });
        totalCount += elements.length;

        // 如果在主页面，向所有iframe发送清除高亮消息
        if (window.self === window.top) {
            const iframes = document.querySelectorAll('iframe');
            iframes.forEach((iframe, index) => {
                try {
                    iframe.contentWindow.postMessage({
                        type: 'INTELLIGENT_PRESENTATION_CLEAR_HIGHLIGHT'
                    }, '*');
                    console.log('向iframe[' + index + ']发送清除高亮消息');
                } catch (e) {
                    console.log('无法向iframe[' + index + ']发送消息:', e.message);
                }
            });
        }

        console.log('清除了当前文档中的', totalCount, '个高亮元素');
        return { success: true, count: totalCount };
    } catch (error) {
        console.error('清除高亮失败:', error);
        return { success: false, error: error.message };
    }
};

// 定义获取信息功能函数
window.intelligentPresentationGetInfo = function() {
    try {
        return {
            success: true,
            data: {
                url: window.location.href,
                title: document.title,
                extensionLoaded: true,
                timestamp: new Date().toISOString()
            }
        };
    } catch (error) {
        console.error('获取信息失败:', error);
        return { success: false, error: error.message };
    }
};

// 定义动态文本获取功能函数
window.intelligentPresentationGetText = function(selector, property = 'textContent') {
    try {
        console.log('🔍 扩展模式获取文本:', selector, '属性:', property);

        // 在当前文档中查找元素
        let element = document.querySelector(selector);
        console.log('当前文档中找到元素:', element ? '是' : '否');

        // 如果在主页面且没找到元素，通过消息传递到所有frame
        if (!element && window.self === window.top) {
            console.log('主页面中未找到元素，向所有frame发送获取文本消息');

            // 向所有iframe发送消息
            const iframes = document.querySelectorAll('iframe');
            iframes.forEach((iframe, index) => {
                try {
                    iframe.contentWindow.postMessage({
                        type: 'GET_DYNAMIC_TEXT',
                        selector: selector,
                        property: property
                    }, '*');
                    console.log('向iframe[' + index + ']发送获取文本消息');
                } catch (e) {
                    console.log('无法向iframe[' + index + ']发送消息:', e.message);
                }
            });

            // 返回null，表示需要等待iframe响应
            return { success: false, error: '元素在iframe中，需要等待响应' };
        }

        if (!element) {
            return { success: false, error: '未找到元素: ' + selector };
        }

        // 提取文本内容
        let content = '';
        switch (property) {
            case 'textContent':
                content = element.textContent?.trim() || '';
                break;
            case 'innerText':
                content = element.innerText?.trim() || '';
                break;
            case 'innerHTML':
                content = element.innerHTML?.trim() || '';
                break;
            case 'value':
                content = element.value || '';
                break;
            default:
                // 尝试作为属性获取
                content = element.getAttribute(property) || element[property] || '';
        }

        console.log('✅ 获取到文本内容:', content);
        return { success: true, content: content };

    } catch (error) {
        console.error('获取文本失败:', error);
        return { success: false, error: error.message };
    }
};

// 为了兼容性，添加旧的函数名别名
window.highlightElement = window.intelligentPresentationHighlight;
window.simulateClick = window.intelligentPresentationClick;
window.clearHighlight = window.intelligentPresentationClearHighlight;
window.getExtensionInfo = window.intelligentPresentationGetInfo;
window.getDynamicText = window.intelligentPresentationGetText;

console.log('✅ 扩展标记已设置');
console.log('✅ 功能函数已定义');
console.log('✅ 兼容性别名已设置');

// 监听来自页面的消息请求
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('📨 Content Script收到消息:', request);

    try {
        switch (request.action) {
            case 'highlight':
                const highlightResult = intelligentPresentationHighlight(
                    request.data.selector,
                    request.data.color,
                    request.data.duration
                );
                sendResponse(highlightResult);
                break;

            case 'click':
                const clickResult = intelligentPresentationClick(
                    request.data.selector,
                    request.data.showIndicator
                );
                sendResponse(clickResult);
                break;

            case 'clearHighlight':
                const clearResult = intelligentPresentationClearHighlight();
                sendResponse(clearResult);
                break;

            case 'getInfo':
                const infoResult = intelligentPresentationGetInfo();
                sendResponse(infoResult);
                break;

            case 'getText':
                const textResult = intelligentPresentationGetText(
                    request.data.selector,
                    request.data.property
                );
                sendResponse(textResult);
                break;

            default:
                sendResponse({ success: false, error: 'Unknown action: ' + request.action });
        }
    } catch (error) {
        console.error('❌ Content Script处理消息时出错:', error);
        sendResponse({ success: false, error: error.message });
    }

    return true; // 保持消息通道开放
});

console.log('✅ Content Script消息监听器已设置');

console.log('🔗 Content Script已准备好处理桥接消息');

// 监听来自页面主世界的消息
window.addEventListener('message', function(event) {
    // 处理桥接消息
    if (event.source === window && event.data && event.data.type === 'INTELLIGENT_PRESENTATION_BRIDGE') {
        console.log('📨 Content Script收到桥接消息:', event.data);

        try {
            let result;
            const { action, selector, options } = event.data;

            switch (action) {
                case 'highlight':
                    result = intelligentPresentationHighlight(selector, options.color, options.duration);
                    break;

                case 'click':
                    result = intelligentPresentationClick(selector, options.showIndicator);
                    break;

                case 'simulateClick':
                    result = simulateClick(selector, options);
                    break;

                case 'clearHighlight':
                    result = intelligentPresentationClearHighlight();
                    break;

                case 'getInfo':
                    result = intelligentPresentationGetInfo();
                    break;

                case 'input':
                    result = (function() {
                        const value = options && options.value;
                        console.log('🎯 输入元素:', selector, '值:', value);
                        
                        // 在当前文档中查找元素
                        let el = document.querySelector(selector);
                        console.log('在当前文档中查找元素:', selector);
                        
                        // 如果在主页面且没找到元素，通过消息传递到所有frame
                        if (!el && window.self === window.top) {
                            console.log('主页面中未找到元素，向所有frame发送输入消息');
                            
                            // 向所有iframe发送消息
                            const iframes = document.querySelectorAll('iframe');
                            iframes.forEach((iframe, index) => {
                                try {
                                    iframe.contentWindow.postMessage({
                                        type: 'INTELLIGENT_PRESENTATION_INPUT',
                                        selector: selector,
                                        value: value,
                                        options: options
                                    }, '*');
                                    console.log('向iframe[' + index + ']发送输入消息');
                                } catch (e) {
                                    console.log('无法向iframe[' + index + ']发送消息:', e.message);
                                }
                            });
                            
                            // 返回成功，因为消息已发送
                            return { success: true, messagesSent: iframes.length };
                        }
                        
                        // 尝试使用index
                        if (!el && options && typeof options.index === 'number') {
                            const all = document.querySelectorAll(selector);
                            if (all[options.index]) el = all[options.index];
                        }
                        
                        if (!el) {
                            return { success: false, error: '未找到输入元素: ' + selector };
                        }
                        
                        console.log('找到输入元素:', el);
                        el.focus && el.focus();
                        el.value = value;
                        el.dispatchEvent(new Event('input', { bubbles: true }));
                        el.dispatchEvent(new Event('change', { bubbles: true }));
                        return { success: true };
                    })();
                    break;

                default:
                    result = { success: false, error: 'Unknown action: ' + action };
            }

            console.log('🌉 桥接API执行结果:', result);

        } catch (error) {
            console.error('❌ 桥接API执行出错:', error);
        }
        return;
    }

    // 处理来自主页面的跨frame高亮消息
    if (event.data && event.data.type === 'INTELLIGENT_PRESENTATION_HIGHLIGHT') {
        console.log('📨 收到跨frame高亮消息:', event.data);

        try {
            const { selector, color, duration } = event.data;
            const result = intelligentPresentationHighlight(selector, color, duration);
            console.log('🎯 跨frame高亮执行结果:', result);
        } catch (error) {
            console.error('❌ 跨frame高亮执行出错:', error);
        }
        return;
    }

    // 处理来自主页面的跨frame点击消息
    if (event.data && event.data.type === 'INTELLIGENT_PRESENTATION_CLICK') {
        console.log('📨 收到跨frame点击消息:', event.data);

        try {
            const { selector, showIndicator } = event.data;
            const result = intelligentPresentationClick(selector, showIndicator);
            console.log('🖱️ 跨frame点击执行结果:', result);
        } catch (error) {
            console.error('❌ 跨frame点击执行出错:', error);
        }
        return;
    }

    // 处理来自主页面的跨frame清除高亮消息
    if (event.data && event.data.type === 'INTELLIGENT_PRESENTATION_CLEAR_HIGHLIGHT') {
        console.log('📨 收到跨frame清除高亮消息');

        try {
            const result = intelligentPresentationClearHighlight();
            console.log('🧹 跨frame清除高亮执行结果:', result);
        } catch (error) {
            console.error('❌ 跨frame清除高亮执行出错:', error);
        }
        return;
    }

    // 处理来自主页面的跨frame输入消息
    if (event.data && event.data.type === 'INTELLIGENT_PRESENTATION_INPUT') {
        console.log('📨 收到跨frame输入消息:', event.data);

        try {
            const { selector, value, options } = event.data;
            let el = document.querySelector(selector);
            
            // 尝试使用index
            if (!el && options && typeof options.index === 'number') {
                const all = document.querySelectorAll(selector);
                if (all[options.index]) el = all[options.index];
            }
            
            if (!el) {
                console.log('❌ 跨frame输入失败: 未找到元素', selector);
                return;
            }
            
            console.log('✅ 跨frame输入成功:', selector, value);
            el.focus && el.focus();
            el.value = value;
            el.dispatchEvent(new Event('input', { bubbles: true }));
            el.dispatchEvent(new Event('change', { bubbles: true }));
        } catch (error) {
            console.error('❌ 跨frame输入执行出错:', error);
        }
        return;
    }

    // 处理来自主页面的跨frame动态文本获取消息
    if (event.data && event.data.type === 'GET_DYNAMIC_TEXT') {
        console.log('📨 收到跨frame动态文本获取消息:', event.data);

        try {
            const { selector, property } = event.data;
            const result = intelligentPresentationGetText(selector, property);

            // 发送响应消息回主页面
            if (result.success) {
                window.parent.postMessage({
                    type: 'DYNAMIC_TEXT_RESPONSE',
                    selector: selector,
                    success: true,
                    content: result.content
                }, '*');
                console.log('✅ 跨frame动态文本获取成功:', result.content);
            } else {
                window.parent.postMessage({
                    type: 'DYNAMIC_TEXT_RESPONSE',
                    selector: selector,
                    success: false,
                    error: result.error
                }, '*');
                console.log('❌ 跨frame动态文本获取失败:', result.error);
            }
        } catch (error) {
            console.error('❌ 跨frame动态文本获取执行出错:', error);
            window.parent.postMessage({
                type: 'DYNAMIC_TEXT_RESPONSE',
                selector: event.data.selector,
                success: false,
                error: error.message
            }, '*');
        }
        return;
    }

    // 处理来自主页面的跨frame元素数量获取消息
    if (event.data && event.data.type === 'GET_ELEMENT_COUNT') {
        console.log('📨 收到跨frame元素数量获取消息:', event.data);

        try {
            const { selector } = event.data;
            const elements = document.querySelectorAll(selector);
            const count = elements.length;

            console.log(`✅ 跨frame元素数量统计成功: ${count} 个 (${selector})`);

            // 发送响应消息回主页面
            window.parent.postMessage({
                type: 'ELEMENT_COUNT_RESPONSE',
                selector: selector,
                success: true,
                count: count
            }, '*');

        } catch (error) {
            console.error('❌ 跨frame元素数量获取执行出错:', error);
            window.parent.postMessage({
                type: 'ELEMENT_COUNT_RESPONSE',
                selector: event.data.selector,
                success: false,
                error: error.message,
                count: 0
            }, '*');
        }
        return;
    }
});







// 在页面上添加一个可见的标记
function createExtensionMarker() {
    const marker = document.createElement('div');
    marker.id = 'extension-marker';
    marker.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        background: #00ff00;
        color: #000;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
        z-index: 999999;
        font-family: Arial, sans-serif;
        cursor: pointer;
        transition: all 0.3s ease;
    `;
    marker.textContent = '✅ 扩展已加载';

    // 关闭标记的函数
    function closeMarker() {
        if (marker && marker.parentNode) {
            marker.classList.add('closing');
            setTimeout(() => {
                if (marker && marker.parentNode) {
                    marker.remove();
                }
            }, 300);
        }
    }

    // 点击关闭功能
    marker.addEventListener('click', closeMarker);

    // 添加到页面
    document.body.appendChild(marker);

    // 3秒后自动关闭
    setTimeout(closeMarker, 3000);
}

// 等待DOM加载后添加标记
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', createExtensionMarker);
} else {
    createExtensionMarker();
}

// 不再需要CSS样式，直接使用内联样式与系统自带高亮保持一致

console.log('✅ 智能讲解助手功能已就绪');

// 注入录制补丁脚本到iframe中
function injectRecorderPatch() {
    // 只在iframe中执行
    if (window.self === window.top) {
        return;
    }
    
    console.log('🎯 注入录制补丁脚本到iframe:', window.location.href);
    
    // 创建script标签注入录制补丁
    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('content-script-patch.js');
    script.onload = function() {
        console.log('✅ 录制补丁脚本已注入');
    };
    script.onerror = function() {
        console.error('❌ 录制补丁脚本注入失败');
    };
    document.head.appendChild(script);
}

// 等待DOM加载后注入录制补丁
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', injectRecorderPatch);
} else {
    injectRecorderPatch();
}

// 调试：验证函数是否正确定义
console.log('🔍 函数检查:');
console.log('  - intelligentPresentationHighlight:', typeof window.intelligentPresentationHighlight);
console.log('  - intelligentPresentationClick:', typeof window.intelligentPresentationClick);
console.log('  - highlightElement (别名):', typeof window.highlightElement);
console.log('  - simulateClick (别名):', typeof window.simulateClick);

// 立即测试函数可用性
setTimeout(() => {
    console.log('🧪 延迟函数检查 (500ms后):');
    console.log('  - intelligentPresentationHighlight:', typeof window.intelligentPresentationHighlight);
    console.log('  - intelligentPresentationClick:', typeof window.intelligentPresentationClick);
    console.log('  - highlightElement (别名):', typeof window.highlightElement);
    console.log('  - simulateClick (别名):', typeof window.simulateClick);
}, 500);
