/**
 * 智能讲解系统 - 主入口文件
 * 负责初始化和启动整个讲解系统
 */

// 全局变量
window.presentationController = null;

/**
 * 初始化讲解系统
 */
async function initPresentationSystem() {
    try {
        // 检查依赖
        if (!checkDependencies()) {
            throw new Error('系统依赖检查失败');
        }

        // 创建控制器实例
        window.presentationController = new PresentationController();

        // 初始化控制器
        const success = await window.presentationController.init();

        if (success) {
            // 配置语音参数
            configureVoiceSettings();

            // 添加启动按钮
            addPresentationButton();

            // 初始化语音控制系统
            await initVoiceControl();

            return true;
        } else {
            throw new Error('控制器初始化失败');
        }

    } catch (error) {
        console.error('智能讲解系统初始化失败:', error);
        return false;
    }
}

/**
 * 检查系统依赖
 */
function checkDependencies() {
    const requiredClasses = [
        'PresentationController',
        'SpeechEngine', 
        'UIController',
        'ErrorHandler'
    ];
    
    for (const className of requiredClasses) {
        if (typeof window[className] === 'undefined') {
            console.error(`缺少依赖类: ${className}`);
            return false;
        }
    }
    
    // 检查讲解脚本
    if (!window.presentationScript || !Array.isArray(window.presentationScript)) {
        console.error('缺少讲解脚本');
        return false;
    }
    
    return true;
}

/**
 * 添加讲解启动按钮
 */
function addPresentationButton() {
    // 不再创建按钮，改为添加快捷键监听
    addPresentationKeyboardShortcuts();
}

function addPresentationKeyboardShortcuts() {
    // 添加键盘快捷键监听
    document.addEventListener('keydown', function(event) {
        // Ctrl + Shift + P 启动/停止智能讲解
        if (event.ctrlKey && event.shiftKey && event.key === 'P') {
            event.preventDefault();
            event.stopPropagation();

            console.log('🎹 快捷键触发: Ctrl+Shift+P');

            // 检查当前状态
            if (window.presentationController && window.presentationController.isActive) {
                stopPresentation();
                console.log('🎹 快捷键: 停止智能讲解');
            } else {
                startPresentation();
                console.log('🎹 快捷键: 启动智能讲解');
            }
        }

        // Ctrl + Shift + Space 暂停/恢复讲解
        if (event.ctrlKey && event.shiftKey && event.code === 'Space') {
            event.preventDefault();
            event.stopPropagation();

            console.log('🎹 快捷键触发: Ctrl+Shift+Space');

            if (window.presentationController && window.presentationController.isActive) {
                if (window.presentationController.isPaused) {
                    window.presentationController.resume();
                    console.log('🎹 快捷键: 恢复讲解');
                } else {
                    window.presentationController.pause();
                    console.log('🎹 快捷键: 暂停讲解');
                }
            }
        }
    });

    console.log('🎹 智能讲解快捷键已启用:');
    console.log('🎹   Ctrl+Shift+P: 启动/停止讲解');
    console.log('🎹   Ctrl+Shift+Space: 暂停/恢复讲解');
}



/**
 * 开始讲解
 */
async function startPresentation() {
    try {
        if (!window.presentationController) {
            throw new Error('讲解控制器未初始化');
        }
        
        // 设置用户交互标志（重要：确保语音能够播放）
        window.speechUserInteraction = true;

        // 隐藏启动按钮
        const startButton = document.getElementById('presentation-start-btn');
        if (startButton) {
            startButton.style.display = 'none';
        }

        // 启动讲解
        await window.presentationController.start();
        
    } catch (error) {
        console.error('启动讲解失败:', error);
        
        // 重新显示启动按钮
        const startButton = document.getElementById('presentation-start-btn');
        if (startButton) {
            startButton.style.display = 'flex';
        }
    }
}

/**
 * 停止讲解
 */
function stopPresentation() {
    if (window.presentationController) {
        window.presentationController.stop();
        
        // 重新显示启动按钮
        const startButton = document.getElementById('presentation-start-btn');
        if (startButton) {
            startButton.style.display = 'flex';
        }
    }
}

/**
 * 显示消息提示
 */
function showMessage(message, type = 'info', duration = 3000) {
    const messageEl = document.createElement('div');
    messageEl.className = `presentation-init-message ${type}`;
    messageEl.textContent = message;
    
    const bgColor = type === 'error' ? '#ff6b6b' : 
                   type === 'success' ? '#4ecdc4' : '#667eea';
    
    messageEl.style.cssText = `
        position: fixed;
        top: 80px;
        right: 20px;
        background: ${bgColor};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        z-index: 10000;
        font-size: 14px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        animation: slideInRight 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
    `;
    
    // 添加动画样式
    if (!document.getElementById('presentation-message-styles')) {
        const style = document.createElement('style');
        style.id = 'presentation-message-styles';
        style.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }
    
    document.body.appendChild(messageEl);
    
    setTimeout(() => {
        messageEl.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 300);
    }, duration);
}

/**
 * 配置语音设置
 */
function configureVoiceSettings() {
    if (window.presentationController && window.presentationController.speechEngine) {
        // 使用默认配置（已经在speech-engine.js中设置好了）
        // 不需要应用预设，直接使用默认的高语速配置
    }
}

/**
 * 初始化语音控制系统
 */
async function initVoiceControl() {
    try {
        // 检查语音控制依赖
        if (!window.VoiceManager) {
            console.warn('语音控制系统不可用');
            return false;
        }

        // 创建语音管理器实例
        window.voiceManager = new window.VoiceManager();

        // 初始化语音管理器
        const success = await window.voiceManager.init();

        if (success) {
            console.log('✅ 语音控制系统初始化成功');
            return true;
        } else {
            console.warn('⚠️ 语音控制系统初始化失败');
            return false;
        }
    } catch (error) {
        console.error('❌ 语音控制系统初始化错误:', error);
        return false;
    }
}

/**
 * 获取系统状态
 */
function getPresentationStatus() {
    if (!window.presentationController) {
        return { status: 'not_initialized' };
    }

    return {
        status: 'ready',
        isActive: window.presentationController.isActive,
        currentStep: window.presentationController.currentStep,
        totalSteps: window.presentationController.script.length,
        isPaused: window.presentationController.isPaused
    };
}

/**
 * 智能初始化函数 - 根据环境自动选择初始化方式
 */
function smartInit() {
    const isInMainPage = window.self === window.top;

    if (isInMainPage) {
        console.log('🎯 主页面环境：智能讲解系统初始化');
        // 在主页面中，延迟初始化确保iframe加载完成
        setTimeout(() => {
            initPresentationSystem();
        }, 1000);
    } else {
        console.log('🎯 iframe环境：智能讲解系统初始化');
        // 在iframe中，立即初始化
        initPresentationSystem();
    }
}

/**
 * 页面加载完成后自动初始化
 */
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', smartInit);
} else {
    smartInit();
}





/**
 * 动态切换语音配置
 * @param {string} voiceName - 语音名称 (如: 'Microsoft Huihui', 'Microsoft Yaoyao')
 * @param {number} rate - 语速 (0.1-10，推荐0.5-2.0)
 * @param {number} pitch - 音调 (0-2，推荐0.8-1.2)
 */
function switchVoiceConfig(voiceName, rate = 1.0, pitch = 1.0) {
    if (window.presentationController && window.presentationController.speechEngine) {
        const speechEngine = window.presentationController.speechEngine;

        // 直接配置语音参数
        speechEngine.configure(voiceName, rate, pitch, 0.8);



        // 测试新配置
        speechEngine.speak('语音配置已更新，这是测试语音。', { rate, pitch, volume: 0.8 });
    } else {
        console.error('❌ 智能讲解系统未初始化');
    }
}

/**
 * 应用预设配置
 * @param {string} presetName - 预设名称 ('huihui', 'yaoyao', 'kangkang')
 */
function applyVoicePreset(presetName) {
    if (window.presentationController && window.presentationController.speechEngine) {
        const speechEngine = window.presentationController.speechEngine;
        speechEngine.applyPreset(presetName);



        // 测试新配置
        const preset = speechEngine.config.presets[presetName];
        if (preset) {
            speechEngine.speak(`已切换到${preset.description}，这是测试语音。`);
        }
    } else {
        console.error('❌ 智能讲解系统未初始化');
    }
}

// 导出主要函数供外部调用
window.PresentationSystem = {
    init: initPresentationSystem,
    start: startPresentation,
    stop: stopPresentation,
    getStatus: getPresentationStatus,
    configureVoice: configureVoiceSettings,
    switchVoice: switchVoiceConfig,
    applyPreset: applyVoicePreset,
    initVoiceControl: initVoiceControl
};

// 添加全局快捷键 - 移除重复的快捷键监听
// 快捷键已在addPresentationKeyboardShortcuts函数中处理