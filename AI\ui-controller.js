/**
 * UI控制器 - 负责讲解界面的显示和控制
 */
class UIController {
    constructor() {
        this.container = null;
        this.controlPanel = null;
        this.textPanel = null;
        this.progressBar = null;
        this.isVisible = false;
        this.currentText = '';
        this.textAnimation = null;
        this.textPosition = 0;
        this.isCollapsed = false;
        this.toggleButton = null;
    }

    /**
     * 初始化UI界面
     */
    async init() {
        this.createContainer();
        this.createControlPanel();
        this.createTextPanel();
        this.createProgressBar();
        this.addStyles();

        // 重要：绑定控制事件
        this.bindControlEvents();


    }

    /**
     * 创建主容器
     */
    createContainer() {
        this.container = document.createElement('div');
        this.container.id = 'presentation-ui';
        this.container.className = 'presentation-ui-container';
        this.container.style.display = 'none';
        
        document.body.appendChild(this.container);
    }

    /**
     * 创建控制面板
     */
    createControlPanel() {
        this.controlPanel = document.createElement('div');
        this.controlPanel.className = 'presentation-control-panel';

        this.controlPanel.innerHTML = `
            <div class="ai-indicator">
                <i class="fas fa-robot ai-icon"></i>
                <span class="ai-label">智能讲解</span>
            </div>
            <div class="control-buttons">
                <button id="presentation-play-pause-btn" class="control-btn play-pause-btn" title="播放/暂停">
                    <i class="fas fa-play"></i>
                </button>
                <button id="presentation-stop-btn" class="control-btn stop-btn" title="停止">
                    <i class="fas fa-stop"></i>
                </button>
                <button id="presentation-prev-btn" class="control-btn prev-btn" title="上一步">
                    <i class="fas fa-step-backward"></i>
                </button>
                <button id="presentation-next-btn" class="control-btn next-btn" title="下一步">
                    <i class="fas fa-step-forward"></i>
                </button>
            </div>
            <div class="control-info">
                <span id="presentation-step-info" class="step-info">0 / 0</span>
            </div>
            <div class="control-close">
                <button id="presentation-close-btn" class="control-btn close-btn" title="关闭">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // 创建折叠按钮并添加到控制面板内
        this.toggleButton = document.createElement('div');
        this.toggleButton.className = 'presentation-toggle-btn';
        this.toggleButton.innerHTML = '<i class="fas fa-chevron-up"></i>';
        this.toggleButton.title = '折叠/展开';
        this.controlPanel.appendChild(this.toggleButton);

        this.container.appendChild(this.controlPanel);
        this.bindControlEvents();
    }

    /**
     * 创建文本显示面板
     */
    createTextPanel() {
        this.textPanel = document.createElement('div');
        this.textPanel.className = 'presentation-text-panel';

        this.textPanel.innerHTML = `
            <div class="text-content">
                <div id="presentation-text" class="presentation-text">
                    <span id="text-read" class="text-read"></span><span id="text-unread" class="text-unread">欢迎使用智能讲解系统</span>
                </div>
            </div>
        `;

        this.container.appendChild(this.textPanel);
    }

    /**
     * 创建进度条
     */
    createProgressBar() {
        this.progressBar = document.createElement('div');
        this.progressBar.className = 'presentation-progress-bar';
        
        this.progressBar.innerHTML = `
            <div class="progress-track">
                <div id="presentation-progress-fill" class="progress-fill"></div>
            </div>
        `;
        
        this.container.appendChild(this.progressBar);
    }

    /**
     * 添加样式
     */
    addStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .presentation-ui-container {
                position: fixed;
                bottom: 80px;
                left: 50%;
                transform: translateX(-50%);
                z-index: 99999;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                user-select: none;
                transition: all 0.3s ease;
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            .presentation-ui-container.collapsed .presentation-text-panel {
                transform: translateY(100%);
                opacity: 0;
                pointer-events: none;
                height: 0;
                margin: 0;
                padding: 0;
            }

            .presentation-ui-container.collapsed .presentation-control-panel {
                margin-bottom: 0;
            }

            .presentation-toggle-btn {
                position: absolute;
                right: 75px;
                top: 50%;
                transform: translateY(-50%);
                width: 30px;
                height: 30px;
                background: rgba(102, 126, 234, 0.9);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                cursor: pointer;
                transition: all 0.3s ease;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
                z-index: 1;
            }

            .presentation-toggle-btn:hover {
                background: rgba(102, 126, 234, 1);
                transform: translateY(-50%) scale(1.1);
            }

            .presentation-control-panel {
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
                border-radius: 25px;
                padding: 15px 25px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                display: flex;
                align-items: center;
                gap: 20px;
                margin-bottom: 15px;
                transition: all 0.3s ease;
                position: relative;
                min-width: 600px;
            }

            .ai-indicator {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px 12px;
                background: linear-gradient(135deg, #00d4ff, #0099cc);
                border-radius: 20px;
                border: 1px solid rgba(0, 212, 255, 0.3);
                box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
                margin-right: 10px;
            }

            .ai-icon {
                font-size: 16px;
                color: #ffffff;
                animation: aiPulse 2s ease-in-out infinite;
            }

            .ai-label {
                font-size: 12px;
                color: #ffffff;
                font-weight: 600;
                text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
            }

            @keyframes aiPulse {
                0%, 100% {
                    transform: scale(1);
                    filter: brightness(1);
                }
                50% {
                    transform: scale(1.1);
                    filter: brightness(1.2);
                }
            }

            .control-buttons {
                display: flex;
                gap: 10px;
            }

            .control-btn {
                width: 40px;
                height: 40px;
                border: none;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.2);
                color: white;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                transition: all 0.3s ease;
                backdrop-filter: blur(5px);
            }

            .control-btn:hover {
                background: rgba(255, 255, 255, 0.3);
                transform: translateY(-2px);
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            }

            .control-btn:active {
                transform: translateY(0);
            }

            .control-btn.active {
                background: rgba(255, 255, 255, 0.4);
                box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
            }

            .control-info {
                display: flex;
                align-items: center;
                gap: 15px;
            }

            .control-close {
                display: flex;
                align-items: center;
                margin-left: 15px;
                position: absolute;
                right: 25px;
                top: 50%;
                transform: translateY(-50%);
            }

            .step-info {
                color: white;
                font-size: 14px;
                font-weight: 500;
                min-width: 60px;
                text-align: center;
            }

            .close-btn {
                background: rgba(255, 107, 107, 0.3);
            }

            .close-btn:hover {
                background: rgba(255, 107, 107, 0.5);
            }

            .presentation-text-panel {
                background: rgba(0, 0, 0, 0.8);
                border-radius: 20px;
                padding: 15px 25px;
                margin-bottom: 0;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.1);
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                width: 600px;
                height: 50px;
                overflow: hidden;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
            }

            .text-content {
                width: 100%;
                height: 100%;
                overflow: hidden;
                position: relative;
                display: flex;
                align-items: center;
            }

            .presentation-text {
                color: #ffffff;
                font-size: 14px;
                line-height: 1.5;
                white-space: nowrap;
                animation: scrollText 15s linear infinite;
                width: 100%;
                text-align: left;
            }

            @keyframes scrollText {
                0% { transform: translateX(100%); }
                100% { transform: translateX(-100%); }
            }

            .presentation-text-panel:hover .presentation-text {
                animation-play-state: paused;
            }

            .text-read {
                color: #00ff88;
                background: linear-gradient(90deg, #00ff88, #00d4ff);
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                font-weight: 500;
            }

            .text-unread {
                color: #ffffff;
                opacity: 0.8;
            }

            .presentation-text.scrolling {
                animation: none;
                transform: translateY(0);
                transition: transform 0.3s ease;
            }

            @keyframes fadeInText {
                from {
                    opacity: 0;
                    transform: translateY(10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            @keyframes textGlow {
                0% {
                    text-shadow: 0 0 5px rgba(0, 255, 136, 0.5);
                }
                50% {
                    text-shadow: 0 0 15px rgba(0, 255, 136, 0.8);
                }
                100% {
                    text-shadow: 0 0 5px rgba(0, 255, 136, 0.5);
                }
            }

            .presentation-progress-bar {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 10px;
                height: 6px;
                overflow: hidden;
                backdrop-filter: blur(5px);
            }

            .progress-track {
                width: 100%;
                height: 100%;
                position: relative;
            }

            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #00ff88, #00d4ff);
                border-radius: 10px;
                transition: width 0.3s ease;
                width: 0%;
                box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
            }

            /* 高亮动画 */
            @keyframes pulse {
                0% {
                    box-shadow: 0 0 20px rgba(0, 255, 136, 0.6);
                    transform: scale(1);
                }
                50% {
                    box-shadow: 0 0 30px rgba(0, 255, 136, 0.8);
                    transform: scale(1.02);
                }
                100% {
                    box-shadow: 0 0 20px rgba(0, 255, 136, 0.6);
                    transform: scale(1);
                }
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                .presentation-ui-container {
                    left: 10px;
                    right: 10px;
                    transform: none;
                }

                .presentation-text-panel {
                    min-width: auto;
                    max-width: none;
                }

                .control-buttons {
                    gap: 8px;
                }

                .control-btn {
                    width: 40px;
                    height: 40px;
                    font-size: 14px;
                }
            }

            /* 隐藏状态 */
            .presentation-ui-container.hidden {
                display: none !important;
            }
        `;
        
        document.head.appendChild(style);
    }

    /**
     * 绑定控制事件 - 简化版本
     */
    bindControlEvents() {
        // 使用延迟确保DOM元素已经创建
        setTimeout(() => {
            this.bindButtonEvents();
        }, 100);
    }

    /**
     * 绑定按钮事件
     */
    bindButtonEvents() {
        try {
            // 折叠/展开按钮
            if (this.toggleButton) {
                this.toggleButton.onclick = (e) => {
                    e.preventDefault();
                    this.toggleCollapse();
                };
            }

            // 播放/暂停按钮
            const playPauseBtn = document.getElementById('presentation-play-pause-btn');
            if (playPauseBtn) {
                playPauseBtn.onclick = (e) => {
                    e.preventDefault();
                    this.handlePlayPauseClick();
                };
            } else {
                console.error('❌ 找不到播放/暂停按钮');
            }

            // 停止按钮
            const stopBtn = document.getElementById('presentation-stop-btn');
            if (stopBtn) {
                stopBtn.onclick = (e) => {
                    e.preventDefault();
                    this.handleStopClick();
                };
            } else {
                console.error('❌ 找不到停止按钮');
            }

            // 上一步按钮
            const prevBtn = document.getElementById('presentation-prev-btn');
            if (prevBtn) {
                prevBtn.onclick = (e) => {
                    e.preventDefault();
                    this.handlePrevClick();
                };
            } else {
                console.error('❌ 找不到上一步按钮');
            }

            // 下一步按钮
            const nextBtn = document.getElementById('presentation-next-btn');
            if (nextBtn) {
                nextBtn.onclick = (e) => {
                    e.preventDefault();
                    this.handleNextClick();
                };
            } else {
                console.error('❌ 找不到下一步按钮');
            }

            // 关闭按钮
            const closeBtn = document.getElementById('presentation-close-btn');
            if (closeBtn) {
                closeBtn.onclick = (e) => {
                    e.preventDefault();
                    this.handleStopClick();
                };
            } else {
                console.error('❌ 找不到关闭按钮');
            }

        } catch (error) {
            console.error('❌ 绑定按钮事件失败:', error);
        }
    }

    /**
     * 处理播放/暂停按钮点击
     */
    handlePlayPauseClick() {
        try {
            if (window.presentationController) {
                if (window.presentationController.isPlaying && !window.presentationController.isPaused) {
                    window.presentationController.pause();
                } else {
                    window.presentationController.resume();
                }
            } else {
                console.error('❌ 讲解控制器不存在');
            }
        } catch (error) {
            console.error('❌ 处理播放/暂停点击失败:', error);
        }
    }

    /**
     * 处理停止按钮点击
     */
    handleStopClick() {
        try {
            if (window.presentationController) {
                window.presentationController.stop();
            } else {
                console.error('❌ 讲解控制器不存在');
            }
        } catch (error) {
            console.error('❌ 处理停止点击失败:', error);
        }
    }

    /**
     * 处理上一步按钮点击
     */
    handlePrevClick() {
        try {
            if (window.presentationController) {
                window.presentationController.previous();
            } else {
                console.error('❌ 讲解控制器不存在');
            }
        } catch (error) {
            console.error('❌ 处理上一步点击失败:', error);
        }
    }

    /**
     * 处理下一步按钮点击
     */
    handleNextClick() {
        try {
            if (window.presentationController) {
                window.presentationController.next();
            } else {
                console.error('❌ 讲解控制器不存在');
            }
        } catch (error) {
            console.error('❌ 处理下一步点击失败:', error);
        }
    }

    /**
     * 切换折叠状态
     */
    toggleCollapse() {
        this.isCollapsed = !this.isCollapsed;
        if (this.isCollapsed) {
            this.container.classList.add('collapsed');
            this.toggleButton.innerHTML = '<i class="fas fa-chevron-down"></i>';
        } else {
            this.container.classList.remove('collapsed');
            this.toggleButton.innerHTML = '<i class="fas fa-chevron-up"></i>';
        }
    }

    /**
     * 切换播放/暂停状态
     */
    togglePlayPause() {
        try {
            const btn = document.getElementById('presentation-play-pause-btn');
            if (!btn) {
                console.error('找不到播放/暂停按钮');
                return;
            }

            const icon = btn.querySelector('i');
            if (!icon) {
                console.error('找不到播放/暂停按钮图标');
                return;
            }

            if (window.presentationController) {
                if (window.presentationController.isPlaying && !window.presentationController.isPaused) {
                    window.presentationController.pause();
                    icon.className = 'fas fa-play';
                    btn.title = '播放';
                } else {
                    window.presentationController.resume();
                    icon.className = 'fas fa-pause';
                    btn.title = '暂停';
                }
            } else {
                console.error('讲解控制器不存在');
            }
        } catch (error) {
            console.error('切换播放/暂停状态失败:', error);
        }
    }

    /**
     * 显示讲解界面
     */
    show() {
        if (this.container) {
            this.container.style.display = 'block';
            this.isVisible = true;

            // 添加显示动画
            this.container.style.opacity = '0';
            this.container.style.transform = 'translateX(-50%) translateY(20px)';

            setTimeout(() => {
                this.container.style.transition = 'all 0.3s ease';
                this.container.style.opacity = '1';
                this.container.style.transform = 'translateX(-50%) translateY(0)';

                // 确保事件绑定正常 - 延迟绑定确保DOM完全渲染
                setTimeout(() => {
                    this.bindButtonEvents();
                }, 100);
            }, 10);
        }
    }

    /**
     * 隐藏讲解界面
     */
    hide() {
        if (this.container) {
            this.container.style.transition = 'all 0.3s ease';
            this.container.style.opacity = '0';
            this.container.style.transform = 'translateX(-50%) translateY(20px)';
            
            setTimeout(() => {
                this.container.style.display = 'none';
                this.isVisible = false;
            }, 300);
        }
    }

    /**
     * 显示讲解文本
     */
    showText(text, forceReset = true) {

        // 强制停止当前动画
        if (this.textAnimation) {
            clearInterval(this.textAnimation);
            this.textAnimation = null;
        }

        this.currentText = text;
        this.textPosition = 0;

        const textElement = document.getElementById('presentation-text');
        const readElement = document.getElementById('text-read');
        const unreadElement = document.getElementById('text-unread');

        if (textElement && readElement && unreadElement) {
            // 立即清空所有内容，确保完全重置
            readElement.textContent = '';
            unreadElement.textContent = '';

            // 显示明显的重置效果
            textElement.style.opacity = '0.3';
            textElement.style.transform = 'scale(0.98)';
            textElement.style.transition = 'all 0.2s ease';

            setTimeout(() => {
                // 设置新文本
                unreadElement.textContent = text;
                textElement.style.opacity = '1';
                textElement.style.transform = 'scale(1)';

                // 延迟开始动画，确保用户看到重置效果
                setTimeout(() => {
                    this.startTextAnimation(text);
                }, 100);
            }, forceReset ? 300 : 150); // 强制重置时延迟更长
        }
    }

    /**
     * 开始文字动画（歌词式进度显示）
     */
    startTextAnimation(text) {
        if (!text || text.length === 0) return;

        const readElement = document.getElementById('text-read');
        const unreadElement = document.getElementById('text-unread');

        if (!readElement || !unreadElement) return;

        // 清除之前的动画
        if (this.textAnimation) {
            clearInterval(this.textAnimation);
            this.textAnimation = null;
        }

        // 重置状态，确保从头开始
        this.textPosition = 0;
        readElement.textContent = '';
        unreadElement.textContent = text;

        // 计算动画速度（根据文本长度调整）
        const animationDuration = Math.max(3000, text.length * 100); // 最少3秒
        const intervalTime = animationDuration / text.length;

        this.textAnimation = setInterval(() => {
            if (this.textPosition >= text.length) {
                clearInterval(this.textAnimation);
                this.textAnimation = null;
                return;
            }

            this.textPosition++;
            const readText = text.substring(0, this.textPosition);
            const unreadText = text.substring(this.textPosition);

            readElement.textContent = readText;
            unreadElement.textContent = unreadText;

            // 添加发光效果到最新读取的字符
            if (this.textPosition > 0) {
                readElement.style.animation = 'textGlow 0.5s ease-in-out';
                setTimeout(() => {
                    if (readElement) {
                        readElement.style.animation = '';
                    }
                }, 500);
            }
        }, intervalTime);
    }

    /**
     * 暂停文字动画
     */
    pauseTextAnimation() {
        if (this.textAnimation) {
            clearInterval(this.textAnimation);
            this.textAnimation = null;
        }
    }

    /**
     * 恢复文字动画
     */
    resumeTextAnimation() {
        if (this.currentText && this.textPosition < this.currentText.length) {
            const remainingText = this.currentText.substring(this.textPosition);
            const remainingDuration = Math.max(1000, remainingText.length * 100);
            const intervalTime = remainingDuration / remainingText.length;

            const readElement = document.getElementById('text-read');
            const unreadElement = document.getElementById('text-unread');

            if (!readElement || !unreadElement) return;

            this.textAnimation = setInterval(() => {
                if (this.textPosition >= this.currentText.length) {
                    clearInterval(this.textAnimation);
                    this.textAnimation = null;
                    return;
                }

                this.textPosition++;
                const readText = this.currentText.substring(0, this.textPosition);
                const unreadText = this.currentText.substring(this.textPosition);

                readElement.textContent = readText;
                unreadElement.textContent = unreadText;

                if (this.textPosition > 0) {
                    readElement.style.animation = 'textGlow 0.5s ease-in-out';
                    setTimeout(() => {
                        if (readElement) {
                            readElement.style.animation = '';
                        }
                    }, 500);
                }
            }, intervalTime);
        }
    }

    /**
     * 更新进度
     */
    updateProgress(current, total) {
        try {
            const progressFill = document.getElementById('presentation-progress-fill');
            const stepInfo = document.getElementById('presentation-step-info');

            if (progressFill && stepInfo) {
                const percentage = Math.min(100, Math.max(0, (current / total) * 100));
                progressFill.style.width = `${percentage}%`;
                stepInfo.textContent = `${current} / ${total}`;

            } else {
                console.error('找不到进度元素:', { progressFill: !!progressFill, stepInfo: !!stepInfo });
            }
        } catch (error) {
            console.error('更新进度失败:', error);
        }
    }

    /**
     * 更新控制按钮状态
     */
    updateControls(state) {
        try {

            const playPauseBtn = document.getElementById('presentation-play-pause-btn');
            const stopBtn = document.getElementById('presentation-stop-btn');
            const prevBtn = document.getElementById('presentation-prev-btn');
            const nextBtn = document.getElementById('presentation-next-btn');

            // 清除所有活动状态
            [playPauseBtn, stopBtn, prevBtn, nextBtn].forEach(btn => {
                if (btn) btn.classList.remove('active');
            });

            // 更新播放/暂停按钮
            this.updatePlayPauseButton(state === 'playing');

            switch (state) {
                case 'playing':
                    if (playPauseBtn) {
                        playPauseBtn.classList.add('active');
                        playPauseBtn.disabled = false;
                    }
                    if (stopBtn) stopBtn.disabled = false;
                    if (prevBtn) prevBtn.disabled = false;
                    if (nextBtn) nextBtn.disabled = false;
                    break;
                case 'paused':
                    if (playPauseBtn) {
                        playPauseBtn.classList.add('active');
                        playPauseBtn.disabled = false;
                    }
                    if (stopBtn) stopBtn.disabled = false;
                    if (prevBtn) prevBtn.disabled = false;
                    if (nextBtn) nextBtn.disabled = false;
                    break;
                case 'stopped':
                case 'completed':
                    if (stopBtn) stopBtn.classList.add('active');
                    // 重置播放按钮为播放状态
                    this.updatePlayPauseButton(false);
                    // 禁用控制按钮
                    [playPauseBtn, stopBtn, prevBtn, nextBtn].forEach(btn => {
                        if (btn) btn.disabled = true;
                    });
                    break;
            }
        } catch (error) {
            console.error('更新控制按钮状态失败:', error);
        }
    }

    /**
     * 更新播放/暂停按钮状态
     */
    updatePlayPauseButton(isPlaying) {
        try {
            const btn = document.getElementById('presentation-play-pause-btn');
            if (!btn) {
                console.error('找不到播放/暂停按钮');
                return;
            }

            const icon = btn.querySelector('i');
            if (!icon) {
                console.error('找不到播放/暂停按钮图标');
                return;
            }

            if (isPlaying) {
                icon.className = 'fas fa-pause';
                btn.title = '暂停';
            } else {
                icon.className = 'fas fa-play';
                btn.title = '播放';
            }
        } catch (error) {
            console.error('更新播放/暂停按钮状态失败:', error);
        }
    }

    /**
     * 显示提示消息
     */
    showMessage(message, type = 'info', duration = 3000) {
        const messageEl = document.createElement('div');
        messageEl.className = `presentation-message ${type}`;
        messageEl.textContent = message;
        messageEl.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'error' ? '#ff6b6b' : '#4ecdc4'};
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            z-index: 10001;
            font-size: 14px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            animation: slideInDown 0.3s ease;
        `;

        document.body.appendChild(messageEl);

        setTimeout(() => {
            messageEl.style.animation = 'slideOutUp 0.3s ease';
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.parentNode.removeChild(messageEl);
                }
            }, 300);
        }, duration);
    }

    /**
     * 获取UI状态
     */
    getStatus() {
        return {
            isVisible: this.isVisible,
            hasContainer: !!this.container
        };
    }
}

// 暴露到全局作用域
window.UIController = UIController;
