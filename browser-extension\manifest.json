{"manifest_version": 3, "name": "智能讲解助手", "version": "1.0.8", "description": "为智能讲解系统提供跨域访问支持，实现页面元素高亮、点击等功能", "permissions": ["activeTab", "scripting", "storage"], "host_permissions": ["http://*/*", "https://*/*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content-script-simple.js"], "css": ["content-script.css"], "run_at": "document_idle", "all_frames": true}, {"matches": ["<all_urls>"], "js": ["main-world-bridge.js"], "run_at": "document_idle", "world": "MAIN"}], "action": {"default_popup": "popup.html", "default_title": "智能讲解助手"}, "web_accessible_resources": [{"resources": ["content-script.js", "content-script-patch.js"], "matches": ["<all_urls>"]}]}