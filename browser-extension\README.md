# 智能讲解助手 - Chrome扩展插件

## 功能介绍

智能讲解助手是一个Chrome浏览器扩展插件，专为智能讲解系统设计，解决跨域访问iframe中第三方页面内容的问题。

### 主要功能

- 🎯 **元素高亮**: 在任何页面（包括iframe中的第三方页面）高亮指定元素
- 🖱️ **元素点击**: 模拟点击页面中的任何元素
- 📜 **页面滚动**: 滚动到指定元素位置
- 📊 **页面信息获取**: 获取页面标题、URL、元素信息等
- 🔄 **双向通信**: 主页面与iframe页面之间的无缝通信

### 技术特点

- ✅ 绕过浏览器同源策略限制
- ✅ 支持所有类型的iframe（包括第三方页面）
- ✅ 无需第三方页面配合
- ✅ 提供降级方案，确保兼容性
- ✅ 安全的消息传递机制

## 安装方法

### 开发者模式安装

1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择本插件的文件夹
6. 插件安装完成

### 使用方法

1. 安装插件后，访问包含iframe的智能讲解系统页面
2. 插件会自动检测并注入到所有页面和iframe中
3. 主页面可以通过插件API操作iframe中的元素

## API使用示例

### 高亮元素
```javascript
chrome.runtime.sendMessage('intelligent-presentation-extension', {
    type: 'PRESENTATION_ACTION',
    action: 'highlight',
    params: {
        selector: '.target-element',
        color: '#ff0000',
        duration: 3000
    }
});
```

### 点击元素
```javascript
chrome.runtime.sendMessage('intelligent-presentation-extension', {
    type: 'PRESENTATION_ACTION',
    action: 'click',
    params: {
        selector: '#submit-button',
        showIndicator: true
    }
});
```

### 滚动到元素
```javascript
chrome.runtime.sendMessage('intelligent-presentation-extension', {
    type: 'PRESENTATION_ACTION',
    action: 'scroll',
    params: {
        selector: '.content-section',
        behavior: 'smooth',
        block: 'center'
    }
});
```

### 获取页面信息
```javascript
chrome.runtime.sendMessage('intelligent-presentation-extension', {
    type: 'PRESENTATION_ACTION',
    action: 'getInfo',
    params: {
        includeElements: true
    }
});
```

### 清除高亮
```javascript
chrome.runtime.sendMessage('intelligent-presentation-extension', {
    type: 'PRESENTATION_ACTION',
    action: 'removeHighlight',
    params: {}
});
```

## 文件结构

```
browser-extension/
├── manifest.json          # 插件配置文件
├── background.js          # 后台脚本（通信桥梁）
├── content-script.js      # 内容脚本（执行操作）
├── popup.html            # 弹窗界面
├── popup.js              # 弹窗脚本
├── icons/                # 插件图标
│   ├── icon16.png
│   ├── icon48.png
│   └── icon128.png
└── README.md             # 说明文档
```

## 权限说明

- `activeTab`: 访问当前活动标签页
- `scripting`: 注入脚本到页面
- `storage`: 存储插件配置
- `host_permissions`: 访问所有网站（用于跨域操作）

## 兼容性

- Chrome 88+
- 支持Manifest V3
- 兼容所有现代网站

## 安全性

- 仅响应来自授权域名的请求
- 限制可执行的操作类型
- 验证消息来源和格式
- 不收集用户数据

## 故障排除

### 插件无法工作
1. 确认插件已正确安装并启用
2. 检查浏览器控制台是否有错误信息
3. 尝试刷新页面重新加载插件

### 跨域操作失败
1. 确认目标页面已完全加载
2. 检查元素选择器是否正确
3. 查看插件弹窗中的状态信息

### 元素无法找到
1. 确认CSS选择器语法正确
2. 检查元素是否存在于DOM中
3. 尝试使用更具体的选择器

## 技术支持

如有问题或建议，请联系开发团队。

## 版本历史

### v1.0.0
- 初始版本发布
- 支持基本的元素操作功能
- 实现跨域通信机制
