* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', sans-serif;
    height: 100%;
    overflow: hidden;
}

html, body {
    height: 100%;
}

.container {
    width: 100%;
    height: 100%;
}

.map-container {
    position: relative;
    width: 100%;
    height: 100%;
}

#map {
    width: 100%;
    height: 100%;
}

.control-panel {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.9);
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    max-width: 300px;
    z-index: 1000;
}

.control-group {
    margin-bottom: 15px;
}

.control-group h3 {
    margin-bottom: 10px;
    color: #333;
    font-size: 16px;
}

.control-item {
    margin-bottom: 8px;
}

.control-item label {
    display: inline-block;
    width: 80px;
    color: #666;
}

select, input[type="text"] {
    padding: 5px;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 150px;
}

button {
    padding: 6px 12px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 5px;
}

button:hover {
    background-color: #45a049;
}

.search-box {
    display: flex;
    gap: 5px;
}

#searchInput {
    flex: 1;
}

#measureResult {
    margin-top: 10px;
    padding: 5px;
    background-color: #f8f8f8;
    border-radius: 4px;
    font-size: 14px;
}

@media (max-width: 768px) {
    .control-panel {
        max-width: 250px;
        padding: 10px;
    }
    
    .control-item label {
        width: 60px;
    }
    
    select, input[type="text"] {
        width: 120px;
    }
}

/* 地块信息对话框样式 */
.dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.dialog-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    width: 400px;
    max-width: 90%;
}

.dialog h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #666;
}

.form-group input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.dialog-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.dialog-buttons button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

#savePlot {
    background-color: #4CAF50;
    color: white;
}

#cancelPlot {
    background-color: #f44336;
    color: white;
}

#savePlot:hover {
    background-color: #45a049;
}

#cancelPlot:hover {
    background-color: #da190b;
}

/* 地块管理按钮样式 */
#drawPlot {
    padding: 8px 16px;
    background-color: #2196F3;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

#drawPlot:hover {
    background-color: #0b7dda;
}

.lasa-label {
    background: rgba(255,255,255,0.85);
    color: #222;
    font-weight: bold;
    border-radius: 4px;
    border: 1px solid #bbb;
    padding: 2px 8px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.15);
    font-size: 14px;
    pointer-events: none;
}

.toggle-panel-btn {
    position: absolute;
    top: 30px;
    right: 320px;
    z-index: 1100;
    width: 32px;
    height: 40px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px 0 0 6px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    font-size: 22px;
    color: #666;
    cursor: pointer;
    transition: right 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-panel.collapsed {
    right: -320px;
    transition: right 0.3s;
}

.toggle-panel-btn.collapsed {
    right: 0;
    border-radius: 0 6px 6px 0;
}

.lasa-glow {
    filter: drop-shadow(0 0 6px #fff) drop-shadow(0 0 12px #2196F3);
    stroke-width: 4px !important;
    stroke: #2196F3 !important;
}

/* 科技感背景装饰 */
.bg-home {
    position: fixed;
    left: 0; top: 0; width: 100vw; height: 100vh;
    z-index: 1;
    object-fit: cover;
    pointer-events: none;
    opacity: 1;
    border: none;
}
.bg-top, .bg-bottom, .bg-left, .bg-right {
    position: absolute;
    z-index: 20;
    pointer-events: none !important;
}
.bg-top { left:0; top:0; width:100%; height:auto; }
.bg-bottom { left:0; bottom:0; width:100%; height:auto; }
.bg-left { left:0; top:0; height:100%; width:auto; }
.bg-right { right:0; top:0; height:100%; width:auto; }
.bg-mask {
    position: absolute;
    left: 0; top: 0; width: 100%; height: 100%;
    z-index: 15;
    background: radial-gradient(52.96% 52.96% at 50% 47.04%, rgba(10, 86, 198, 0) 0, rgba(35, 91, 54, .169) 48%, rgba(0, 24, 37, .6) 100%);
    pointer-events: none !important;
}
.bg-title {
    position: absolute;
    top: 0;
    left: 50%;
    width: 600px;
    height: 55px;
    transform: translateX(-50%);
    z-index: 30;
    color: transparent;
    font-size: 1.8rem;
    font-weight: bold;
    letter-spacing: 4px;
    text-shadow: none;
    font-family: 'Microsoft YaHei','Arial',sans-serif;
    pointer-events: none;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1.2;
    background: linear-gradient(180deg, 
        #ffffff 0%,
        #00fff7 25%,
        #2196f3 50%,
        #00fff7 75%,
        #ffffff 100%
    );
    -webkit-background-clip: text;
    background-clip: text;
    animation: titleGradient 4s ease-in-out infinite;
    background-size: 100% 300%;
}

@keyframes titleGradient {
    0% {
        background-position: 0% 0%;
    }
    50% {
        background-position: 0% 100%;
    }
    100% {
        background-position: 0% 0%;
    }
}

.bg-top {
    height: 80px;
    object-fit: contain;
}
#map, #cesiumContainer {
    position: relative;
    z-index: 10;
    background: transparent !important;
}
.control-panel, .toggle-panel-btn {
    z-index: 40;
}
.bottom-bar-wrap {
    position: absolute;
    left: 0; right: 0; bottom: 0;
    height: 80px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
    z-index: 25;
}
.bg-bottom {
    position: absolute;
    left: 0; bottom: 0;
    height: 50px;
    width: 100%;
    z-index: 1;
    pointer-events: none;
}
.bottom-tab {
    position: absolute;
    left: 50%;
    bottom: 12px;
    transform: translateX(-50%);
    z-index: 30;
    max-width: 90vw;
    padding: 12px 48px 16px 48px;
    background: linear-gradient(90deg, rgba(20,90,180,0.98) 0%, rgba(0,40,80,0.98) 100%);
    color: #fff;
    font-size: clamp(1rem, 2.5vw, 1.25rem);
    font-weight: bold;
    border-radius: 22px 22px 36px 36px/22px 22px 48px 48px;
    box-shadow: none;
    text-shadow: none;
    letter-spacing: 4px;
    border: 2px solid #1a3a5c;
    pointer-events: none;
    user-select: none;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 0 auto;
    transition: bottom 0.2s, font-size 0.2s;
    backdrop-filter: blur(1px);
}
@media (max-width: 600px) {
    .bottom-bar-wrap { height: 48px; }
    .bg-bottom { height: 48px; }
    .bottom-tab {
        bottom: 2px;
        padding: 6px 12px 10px 12px;
        font-size: 1rem;
        border-radius: 14px 14px 24px 24px/14px 14px 32px 32px;
    }
}

.leaflet-control-attribution {
    display: none !important;
}

.project-label-tech {
  background: linear-gradient(90deg, #00fff7 0%, #2196f3 60%, #fff 100%);
  color: #003366;
  font-weight: bold;
  border-radius: 12px;
  border: 2px solid #00fff7;
  padding: 6px 18px;
  box-shadow: 0 0 16px #00fff7, 0 0 32px #2196f3, 0 0 8px #fff;
  font-size: 20px;
  text-shadow: 0 0 12px #00fff7, 0 0 24px #2196f3, 0 2px 8px #fff;
  letter-spacing: 2px;
  font-family: 'Orbitron', 'Microsoft YaHei', Arial, sans-serif;
  animation: neon-flicker 1.5s infinite alternate;
  pointer-events: none;
  user-select: none;
}
@keyframes neon-flicker {
  0% { box-shadow: 0 0 8px #00fff7, 0 0 16px #2196f3, 0 0 4px #fff; }
  100% { box-shadow: 0 0 32px #00fff7, 0 0 48px #2196f3, 0 0 16px #fff; }
}

.view-project-btn {
  position: fixed;
  right: 36px;
  bottom: 36px;
  z-index: 1200;
  padding: 14px 36px;
  background: #00bcd4;
  color: #003366;
  font-size: 20px;
  font-weight: bold;
  border: 2px solid #00bcd4;
  border-radius: 16px;
  box-shadow: none;
  cursor: pointer;
  letter-spacing: 2px;
  font-family: 'Orbitron', 'Microsoft YaHei', Arial, sans-serif;
  transition: background 0.2s, box-shadow 0.2s;
}
.view-project-btn:hover {
  background: #2196f3;
  box-shadow: none;
}

.china-label {
  background: rgba(0,255,247,0.85);
  color: #003366;
  font-weight: bold;
  border-radius: 8px;
  border: 1.5px solid #00fff7;
  padding: 4px 14px;
  box-shadow: 0 0 8px #00fff7, 0 0 16px #2196f3;
  font-size: 16px;
  text-shadow: 0 0 8px #00fff7, 0 0 16px #2196f3;
  pointer-events: none;
  user-select: none;
}

.leaflet-interactive:focus {
  outline: none !important;
  box-shadow: none !important;
}

.layer-control-panel {
  position: absolute;
  top: 32px;
  right: 36px;
  z-index: 1201;
  background: rgba(10, 30, 60, 0.92);
  border-radius: 12px;
  box-shadow: none;
  padding: 18px 22px 18px 22px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  border: 1.5px solid #1a3a5c;
}
.layer-control-panel label {
  color: #00fff7;
  font-size: 17px;
  font-family: 'Orbitron', 'Microsoft YaHei', Arial, sans-serif;
  font-weight: bold;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  text-shadow: none;
}
.layer-control-panel input[type="checkbox"] {
  accent-color: #00fff7;
  width: 18px;
  height: 18px;
  margin-right: 4px;
}

/* 设备名称科技感标签 */
.device-label-tech {
  background: linear-gradient(90deg, #00fff7 0%, #2196f3 60%, #fff 100%);
  color: #003366;
  font-weight: bold;
  border-radius: 10px;
  border: 2px solid #00fff7;
  padding: 4px 18px;
  box-shadow: 0 0 12px #00fff7, 0 0 24px #2196f3, 0 2px 8px #fff;
  font-size: 18px;
  text-shadow: 0 0 8px #00fff7, 0 0 16px #2196f3, 0 2px 8px #fff;
  letter-spacing: 1px;
  font-family: 'Orbitron', 'Microsoft YaHei', Arial, sans-serif;
  pointer-events: none;
  user-select: none;
}

/* 设备弹窗科技感样式 */
.device-popup {
  background: rgba(10, 30, 60, 0.96);
  border: 2px solid #00fff7;
  border-radius: 16px;
  box-shadow: 0 0 24px #00fff7, 0 0 48px #2196f3;
  color: #fff;
  min-width: 320px;
  max-width: 400px;
  padding: 22px 32px 18px 32px;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  position: relative;
}
.device-popup-title {
  font-size: 20px;
  font-weight: bold;
  color: #00fff7;
  margin-bottom: 10px;
  letter-spacing: 1px;
  text-shadow: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.device-popup-row {
  font-size: 16px;
  margin-bottom: 6px;
  color: #e0f7fa;
  word-break: break-all;
}
.device-popup-close {
  position: absolute;
  top: 8px;
  right: 10px;
  background: none;
  border: none;
  color: #00fff7;
  font-size: 22px;
  font-weight: bold;
  cursor: pointer;
  z-index: 2;
  padding: 0 4px;
  transition: color 0.2s;
}
.device-popup-close:hover {
  color: #fff;
}

/* 覆盖Leaflet弹窗默认样式，确保自定义弹窗美观 */
.leaflet-popup-content-wrapper {
  background: none !important;
  box-shadow: none !important;
  border-radius: 18px !important;
  border: none !important;
  padding: 0 !important;
}
.leaflet-popup-content {
  margin: 0 !important;
  padding: 0 !important;
  width: auto !important;
  min-width: 0 !important;
}
.leaflet-popup-tip {
  background: none !important;
  box-shadow: none !important;
  display: none !important;
}
.leaflet-popup-close-button {
  display: none !important;
}

/* 林周县发光效果 */
.linzhou-glow {
    filter: drop-shadow(0 0 16px #FFD700) drop-shadow(0 0 32px #FFD700);
    stroke: #FFD700;
    stroke-width: 4;
}

/* 林周县标签样式（竖排突出，蓝色科技感） */
.linzhou-label {
    background: linear-gradient(180deg, #e0f7fa 0%, #00bcd4 100%);
    color: #0076b6;
    font-weight: bold;
    border: 2px solid #00eaff;
    border-radius: 10px;
    box-shadow: 0 0 10px #00eaff, 0 0 20px #2196f3;
    padding: 10px 4px;
    font-size: 18px;
    letter-spacing: 2px;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    text-shadow: 0 0 6px #00eaff, 0 0 12px #2196f3;
    pointer-events: none;
    user-select: none;
    opacity: 1;
    line-height: 1.5;
    writing-mode: vertical-rl;
    text-align: center;
    background-blend-mode: lighten;
}

/* 其它项目地竖排弱化标签 */
.project-label-weak3 {
    background: linear-gradient(180deg, rgba(0,255,247,0.13) 0%, rgba(33,150,243,0.10) 100%);
    color: #2196f3;
    font-weight: normal !important;
    border-radius: 8px;
    border: 1.5px solid #00eaff;
    box-shadow: 0 0 4px #00eaff, 0 0 8px #2196f3;
    padding: 8px 3px;
    font-size: 15px;
    letter-spacing: 1.5px;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    text-shadow: 0 0 2px #00eaff, 0 0 4px #2196f3;
    pointer-events: none;
    user-select: none;
    opacity: 0.7;
    line-height: 1.45;
    writing-mode: vertical-rl;
    text-align: center;
    background-blend-mode: lighten;
}

/* 弱化项目地标签样式 */
.project-label-weak {
    background: rgba(255,255,255,0.7);
    color: #888;
    font-weight: normal;
    border-radius: 8px;
    border: 1px solid #ccc;
    box-shadow: none;
    padding: 2px 10px;
    font-size: 15px;
    letter-spacing: 0.5px;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    text-shadow: none;
    pointer-events: none;
    user-select: none;
}

/* 极度弱化项目地标签样式 */
.project-label-weak2 {
    background: rgba(255,255,255,0.18);
    color: #b0b0b0;
    font-weight: normal !important;
    border-radius: 6px;
    border: none;
    box-shadow: none;
    padding: 1px 8px;
    font-size: 13px;
    letter-spacing: 0.2px;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    text-shadow: none;
    pointer-events: none;
    user-select: none;
    opacity: 0.7;
}

.dikuai-label-tooltip {
    background: rgba(20,40,80,0.78);
    color: #00fff7;
    font-weight: bold;
    font-size: 16px;
    border-radius: 6px;
    border: 1.5px solid #00fff7;
    box-shadow: 0 2px 8px rgba(0,40,80,0.10);
    padding: 2px 8px;
    text-align: center;
    pointer-events: none;
    letter-spacing: 2px;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    text-shadow: 0 0 8px #00fff7, 0 0 2px #fff;
}

/* 强制修正Leaflet SVG交互 */
.leaflet-overlay-pane svg,
.leaflet-overlay-pane svg path,
.leaflet-interactive {
    pointer-events: auto !important;
}

/* 项目介绍侧栏样式 */
.project-intro-panel {
    position: fixed;
    left: 30px;
    top: 65px;
    bottom: 65px;
    width: 450px;
    height: auto;
    max-height: none;
    background: linear-gradient(90deg, rgba(20,60,120,0.92) 80%, rgba(20,60,120,0.15) 100%);
    box-shadow: 2px 0 18px 0 rgba(0,40,80,0.10);
    border-radius: 0 18px 18px 0;
    z-index: 1202;
    display: flex;
    flex-direction: column;
    padding: 0;
    overflow: visible;
    /* 高度由top/bottom撑开，无需max-height */
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    transition: left 0.5s ease-out; /* 添加 left 属性的过渡动画 */
}

.project-intro-panel.hidden {
    left: -550px; /* 将面板移到屏幕左侧外面，500px 宽度 + 30px 左边距 + 额外一点空间 */
}

.project-intro-header {
    display: flex;
    align-items: center;
    height: 48px;
    background: linear-gradient(90deg, #2176c7 80%, rgba(33,118,199,0.12) 100%);
    border-radius: 0 12px 0 0;
    padding: 0 0 0 18px;
    font-size: 22px;
    font-weight: bold;
    color: #fff;
    letter-spacing: 2px;
    box-shadow: 0 2px 8px rgba(0,40,80,0.08);
    border-left: 4px solid #4fc3f7;
}
.project-intro-icon {
    width: 28px;
    height: 28px;
    margin-right: 10px;
    vertical-align: middle;
}
.project-intro-title {
    font-size: 20px;
    font-weight: bold;
    color: #fff;
    letter-spacing: 2px;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
}
.project-intro-text {
    padding: 0px 32px 12px 32px;
    color: #e3f2fd;
    font-size: 17px;
    line-height: 1.7;
    flex: 1 1 auto;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    overflow: visible;
    min-height: 60px;
    max-height: none;
}
.project-intro-video {
    padding: 22px 32px 10px 32px;
    flex: 0 0 auto;
    display: flex;
    align-items: flex-end;
}
.project-intro-video video {
    height:217px;
    width: 100%;
    max-height: 220px;
    object-fit: cover;
    background: #222;
    border-radius: 8px;
}
@media (max-width: 700px) {
    .project-intro-panel {
        width: 98vw;
        left: 0;
        top: 65px;
        bottom: 65px;
        border-radius: 0 0 18px 18px;
        max-height: none;
    }
    .project-intro-header, .project-intro-text, .project-intro-video {
        padding-left: 12px;
        padding-right: 12px;
    }
    .project-intro-video video {
        max-height: 160px;
    }
}

/* --- 区县导航整体容器 --- */
.project-nav-wrap {
    position: fixed;
    left: 24px;
    top: 24px;
    bottom: 32px;
    width: 270px;
    z-index: 1203;
    display: flex;
    flex-direction: column;
    background: none;
    border: none;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    overflow: visible;
    pointer-events: auto;
}

.project-nav-back {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    padding: 18px 22px 0 18px;
    gap: 0;
}
.project-nav-back-btn {
    background: #022950 !important;
    color: #00bcd4;
    font-size: 17px;
    font-weight: bold;
    border: 1.5px solid #00bcd4;
    border-radius: 14px;
    box-shadow: none;
    cursor: pointer;
    letter-spacing: 1.2px;
    font-family: 'Orbitron', 'Microsoft YaHei', Arial, sans-serif;
    padding: 12px 0;
    width: 180px;
    margin: 0 auto 18px auto;
    display: block;
    text-align: center;
    transition: background 0.2s, color 0.2s, border 0.2s;
    outline: none;
}
.project-nav-back-btn:hover {
    background: #022950 !important;
    color: #0097a7;
    border-color: #0097a7;
    box-shadow: none;
}
.project-nav-title {
    font-size: 22px;
    font-weight: bold;
    color: #00fff7;
    letter-spacing: 3px;
    text-align: center;
    flex: 1;
    padding: 0 0 0 0;
    background: none;
    border-bottom: 2px solid rgba(0,255,247,0.22);
    margin-bottom: 18px;
    text-shadow: none;
}

/* 卡片分组容器 */
.project-nav-list {
    flex: 1;
    overflow-y: auto;
    margin-top: 6px;
    padding-bottom: 18px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}
.project-nav-card {
    background: rgba(20,60,120,0.92);
    border-radius: 20px;
    box-shadow: 0 4px 24px 0 rgba(0,40,80,0.10);
    padding: 14px 12px 8px 12px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    border: 2px solid #00fff7;
    position: relative;
}
.project-nav-card:not(:first-child) {
    margin-top: 0;
}
.project-nav-city {
    font-weight: bold;
    font-size: 18px;
    color: #00fff7;
    margin-bottom: 10px;
    letter-spacing: 2px;
    text-shadow: none;
    border-left: 4px solid #00fff7;
    padding-left: 10px;
    background: none;
}
.project-nav-items {
    display: flex;
    flex-direction: column;
    gap: 14px;
}
.project-nav-item {
    padding: 12px 0;
    color: #e0f7fa;
    font-size: 15px;
    font-weight: bold;
    letter-spacing: 1px;
    text-align: center;
    background: rgba(0,40,80,0.55);
    border: 1.5px solid rgba(0,255,247,0.25);
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.22s cubic-bezier(.4,2,.6,1);
    user-select: none;
    text-shadow: none;
    box-shadow: 0 2px 12px 0 rgba(0,255,247,0.08);
    font-family: 'Microsoft YaHei', Arial, sans-serif;
}
.project-nav-item:hover {
    background: #00fff7;
    color: #003366;
    border-color: #00fff7;
    box-shadow: 0 4px 16px 0 #00fff7;
}
.project-nav-item.active {
    background: linear-gradient(90deg, #00fff7 60%, #00bcd4 100%) !important;
    border-color: #00bcd4 !important;
    color: #003366 !important;
    box-shadow: 0 0 8px 0 #00fff733, 0 2px 8px 0 #00bcd455 inset !important;
    text-shadow: 0 1px 4px #fff, 0 0 2px #00fff7cc;
    transform: translateX(6px) scale(1.04);
    font-weight: bold;
    font-size: 16px;
}

@media (max-width: 700px) {
    .project-nav-wrap {
        width: 96vw;
    }
    .project-nav-card {
        padding: 8px 4px 4px 4px;
    }
    .project-nav-item {
        font-size: 14px;
        padding: 8px 0;
    }
    .project-nav-item.active {
        font-size: 15px;
    }
}

/* 优化底部导航栏边框和高亮效果，适合大屏展示 */
.main-bottom-nav {
  position: fixed;
  left: 0; right: 0; bottom: 10px;
  z-index: 1205;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  width: 420px;
  max-width: 96vw;
  margin: 0 auto;
  background: rgba(0, 40, 80, 0.92);
  border-radius: 32px;
  box-shadow: 0 0 24px #00fff733, 0 2px 16px #00336655;
  padding: 0;
  height: 56px;
  overflow: hidden;
  border: none;
  outline: none;
}
.main-bottom-nav-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 56px;
  font-size: 18px;
  font-weight: bold;
  color: #00e0e6;
  background: transparent;
  border: none;
  outline: none;
  text-decoration: none;
  letter-spacing: 2px;
  font-family: 'Orbitron', 'Microsoft YaHei', Arial, sans-serif;
  transition: background 0.18s, color 0.18s, box-shadow 0.18s;
  box-shadow: none;
  border-radius: 0;
  user-select: none;
  border-bottom: 2.5px solid transparent;
}
.main-bottom-nav-btn.active {
  background: linear-gradient(100deg, #00fff7 0%, #4fd1ff 40%, #b2ebf2 100%);
  color: #003366;
  box-shadow: 0 0 16px #00fff755, 0 2px 12px #00bcd422 inset;
  border-bottom: 2.5px solid #00bcd4;
  text-shadow: 0 1px 8px #fff, 0 0 2px #00fff7cc;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: bold;
}
.main-bottom-nav-btn i {
  font-size: 1.2em;
  color: #00fff7;
  filter: drop-shadow(0 0 4px #00fff7aa);
  margin-right: 4px;
  transition: color 0.18s;
}
.main-bottom-nav-btn.active i {
  color: #2196f3;
  filter: drop-shadow(0 0 6px #4fd1ffcc);
}
.main-bottom-nav-btn:hover:not(.active) {
  background: rgba(0,188,212,0.18);
  color: #fff;
  box-shadow: 0 0 8px #00fff733;
}
.main-bottom-nav-btn:first-child { border-top-left-radius: 32px; border-bottom-left-radius: 32px; }
.main-bottom-nav-btn:last-child { border-top-right-radius: 32px; border-bottom-right-radius: 32px; }

@media (max-width: 700px) {
  .main-bottom-nav {
    width: 98vw;
    height: 40px;
    border-radius: 18px;
  }
  .main-bottom-nav-btn, .main-bottom-nav-btn.active {
    font-size: 15px;
    height: 40px;
    border-radius: 0;
  }
  .main-bottom-nav-btn:first-child { border-top-left-radius: 18px; border-bottom-left-radius: 18px; }
  .main-bottom-nav-btn:last-child { border-top-right-radius: 18px; border-bottom-right-radius: 18px; }
}

/* 青稞实况页面顶部紧贴优化 */
.qingke-overview .bg-title {
  top: 0 !important;
  margin-top: 0 !important;
  padding-top: 0 !important;
}
.qingke-overview .bg-top {
  top: 0 !important;
  margin-top: 0 !important;
  padding-top: 0 !important;
}
.qingke-overview .main-content-placeholder {
  margin-top: 48px !important; /* 避免内容被标题遮挡，可根据实际视觉微调 */
}

/* 视频监控布局样式 */
.video-monitor-container {
    position: fixed;
    top: 80px;
    left: 0;
    right: 0;
    bottom: 80px;
    display: flex;
    gap: 20px;
    padding: 0 20px;
    z-index: 1000;
}

/* 左侧导航面板 */
.monitor-nav-panel {
    width: 300px;
    background: rgba(20, 60, 120, 0.92);
    border-radius: 12px;
    border: 2px solid #00fff7;
    box-shadow: 0 0 20px rgba(0, 255, 247, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.monitor-nav-header {
    padding: 15px;
    border-bottom: 1px solid rgba(0, 255, 247, 0.2);
}

.monitor-nav-header h3 {
    color: #00fff7;
    font-size: 18px;
    margin-bottom: 15px;
    text-align: center;
    letter-spacing: 2px;
}

.monitor-search {
    display: flex;
    gap: 8px;
}

.monitor-search input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #00fff7;
    border-radius: 6px;
    background: rgba(0, 40, 80, 0.8);
    color: #fff;
    font-size: 14px;
}

.monitor-search input::placeholder {
    color: rgba(0, 255, 247, 0.6);
}

.search-btn {
    padding: 8px 12px;
    background: #00fff7;
    border: none;
    border-radius: 6px;
    color: #003366;
    cursor: pointer;
    transition: all 0.3s;
}

.search-btn:hover {
    background: #00bcd4;
}

.monitor-nav-content {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
}

.monitor-city-group {
    margin-bottom: 20px;
    border: 1px solid rgba(0, 255, 247, 0.2);
    border-radius: 8px;
    background: rgba(0, 40, 80, 0.3);
    overflow: hidden;
}

.monitor-city-title {
    color: #00fff7;
    font-size: 16px;
    font-weight: bold;
    padding: 12px 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.3s;
    background: rgba(0, 255, 247, 0.1);
    border-bottom: 1px solid rgba(0, 255, 247, 0.2);
}

.monitor-city-title:hover {
    background: rgba(0, 255, 247, 0.2);
}

.city-title-left {
    display: flex;
    align-items: center;
    gap: 8px;
}

.city-expand-icon {
    transition: transform 0.3s ease;
    font-size: 12px;
}

.city-stats {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 12px;
    color: #b3e5fc;
}

.device-count {
    background: rgba(76, 175, 80, 0.2);
    padding: 2px 6px;
    border-radius: 10px;
    border: 1px solid #4caf50;
}

.online-count {
    background: rgba(0, 255, 247, 0.2);
    padding: 2px 6px;
    border-radius: 10px;
    border: 1px solid #00fff7;
}

.monitor-county-list {
    margin-left: 0;
    padding: 0 15px 15px 15px;
    transition: all 0.3s ease;
    overflow: hidden;
}

/* 折叠/展开状态 */
.monitor-city-group.expanded .city-expand-icon {
    transform: rotate(90deg);
}

.monitor-city-group.collapsed .monitor-county-list {
    max-height: 0 !important;
    padding-top: 0;
    padding-bottom: 0;
}

.monitor-city-group.expanded .monitor-county-list {
    max-height: 2000px;
}

.monitor-county-item {
    margin-bottom: 15px;
    background: rgba(0, 20, 40, 0.3);
    border-radius: 6px;
    padding: 10px;
    border: 1px solid rgba(0, 255, 247, 0.1);
}

.monitor-county-title {
    color: #e0f7fa;
    font-size: 14px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.county-device-count {
    color: #81c784;
    font-size: 12px;
    margin-left: auto;
}

.monitor-device-list {
    margin-left: 0;
    margin-top: 8px;
}

.monitor-device-item {
    padding: 8px 12px;
    background: rgba(0, 40, 80, 0.5);
    border: 1px solid rgba(0, 255, 247, 0.2);
    border-radius: 6px;
    margin-bottom: 8px;
    color: #fff;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 8px;
}

.monitor-device-item:hover {
    background: rgba(0, 255, 247, 0.1);
    border-color: #00fff7;
}

.monitor-device-item.active {
    background: #00fff7;
    color: #003366;
}

/* 视频监控主区域 */
.monitor-main-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.monitor-control-bar {
    display: flex;
    justify-content: flex-end;
    padding: 10px;
}

.grid-switch {
    display: flex;
    gap: 10px;
}

.grid-btn {
    padding: 8px 16px;
    background: rgba(0, 40, 80, 0.8);
    border: 1px solid #00fff7;
    border-radius: 6px;
    color: #00fff7;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 6px;
}

.grid-btn:hover {
    background: rgba(0, 255, 247, 0.1);
}

.grid-btn.active {
    background: #00fff7;
    color: #003366;
}

/* 视频宫格容器 */
.video-grid-container {
    flex: 1;
    display: grid;
    gap: 8px;
    padding: 15px;
    background: rgba(20, 60, 120, 0.92);
    border-radius: 12px;
    border: 2px solid #00fff7;
    box-shadow: 0 0 20px rgba(0, 255, 247, 0.2);
}

/* 宫格布局 */
.grid-1 {
    grid-template-columns: 1fr;
}

.grid-4 {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
}

.grid-9 {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
}

/* 视频画面项 */
.video-grid-item {
    position: relative;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.video-wrapper {
    flex: 1;
    position: relative;
}

.monitor-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-title {
    position: absolute;
    top: 10px;
    left: 10px;
    background: rgba(0, 40, 80, 0.8);
    color: #00fff7;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
}

/* 云台控制 */
.ptz-control {
    padding: 10px;
    display: flex;
    justify-content: center;
    gap: 8px;
    background: rgba(0, 40, 80, 0.8);
}

.ptz-btn {
    width: 36px;
    height: 36px;
    border: 1px solid #00fff7;
    border-radius: 6px;
    background: transparent;
    color: #00fff7;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ptz-btn:hover {
    background: rgba(0, 255, 247, 0.1);
}

/* 响应式布局 */
@media (max-width: 1200px) {
    .video-monitor-container {
        flex-direction: column;
    }
    
    .monitor-nav-panel {
        width: 100%;
        height: 200px;
    }
    
    .monitor-main-area {
        height: calc(100% - 220px);
    }
}

@media (max-width: 768px) {
    .video-monitor-container {
        top: 60px;
        bottom: 60px;
        padding: 10px;
    }
    
    .grid-btn {
        padding: 6px 12px;
        font-size: 14px;
    }
    
    .ptz-btn {
        width: 32px;
        height: 32px;
    }
}

/* 画面格式控制按钮（仅图标，圆形，间隔适中） */
.monitor-grid-switch.icon-only {
    display: flex;
    gap: 16px;
    justify-content: center;
    margin: 18px 0 10px 0;
}
.monitor-grid-switch.icon-only .grid-btn {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    background: #0a1a2f;
    border: 1.5px solid #00fff7;
    color: #00fff7;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    cursor: pointer;
    padding: 0;
}
.monitor-grid-switch.icon-only .grid-btn.active,
.monitor-grid-switch.icon-only .grid-btn:hover {
    background: #00fff7;
    color: #003366;
    border-color: #00bcd4;
}

/* 云台控制十字布局 */
.ptz-cross-layout {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 18px;
    padding-bottom: 10px;
}
.ptz-cross-row {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 2px 0;
    gap: 16px;
}
.ptz-cross-row .ptz-btn {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    background: #0a1a2f;
    border: 1.5px solid #00fff7;
    color: #00fff7;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    cursor: pointer;
    padding: 0;
}
.ptz-cross-row .ptz-btn:hover,
.ptz-cross-row .ptz-btn:focus {
    background: #00fff7;
    color: #003366;
    border-color: #00bcd4;
}
.ptz-center-placeholder {
    width: 38px;
    height: 38px;
    display: inline-block;
}
.zoom-group {
    gap: 24px;
    margin-top: 8px;
}

/* 左右区域高度一致，整体协调 */
.video-monitor-container {
    display: flex;
    align-items: stretch;
    height: calc(100vh - 120px); /* 头部+底部留白 */
    min-height: 500px;
}
.monitor-nav-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-width: 270px;
    max-width: 340px;
    background: rgba(20, 60, 120, 0.92);
    border-radius: 12px;
    border: 2px solid #00fff7;
    box-shadow: 0 0 20px rgba(0, 255, 247, 0.2);
    overflow: hidden;
}
.monitor-nav-header {
    flex-shrink: 0;
}
.monitor-nav-content {
    flex: 1 1 auto;
    overflow-y: auto;
    padding: 15px;
}
.monitor-ptz-panel {
    flex-shrink: 0;
}
.monitor-main-area {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    padding: 24px 0 40px 0;
    height: 100%;
}
.video-grid-container {
    flex: 1 1 auto;
    display: grid;
    align-items: stretch;
    height: 100%;
    background: rgba(20, 60, 120, 0.92);
    border-radius: 12px;
    border: 2px solid #00fff7;
    box-shadow: 0 0 20px rgba(0, 255, 247, 0.2);
    padding: 15px;
}
.grid-1 {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
}
.grid-4 {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
}
.grid-9 {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
}
.video-grid-item {
    background: #000;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    height: 100%;
}
.video-wrapper {
    flex: 1 1 auto;
    position: relative;
    height: 100%;
}
.monitor-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    background: #000;
}
.video-title {
    position: absolute;
    top: 10px;
    left: 10px;
    background: rgba(0, 40, 80, 0.8);
    color: #00fff7;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
}

@media (max-width: 900px) {
    .video-monitor-container {
        flex-direction: column;
        height: auto;
        min-height: 0;
    }
    .monitor-nav-panel {
        width: 100%;
        max-width: none;
        min-width: 0;
        margin-bottom: 10px;
        height: auto;
    }
    .monitor-main-area {
        padding-bottom: 60px;
        height: auto;
    }
    .video-grid-container {
        height: 300px;
    }
}

.fullscreen-btn {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    background: #0a1a2f;
    border: 1.5px solid #00fff7;
    color: #00fff7;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    cursor: pointer;
    margin-left: 16px;
}
.fullscreen-btn:hover, .fullscreen-btn:focus {
    background: #00fff7;
    color: #003366;
    border-color: #00bcd4;
}
.ptz-title {
    color: #00bcd4;
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 14px;
    letter-spacing: 2px;
    display: flex;
    align-items: center;
    gap: 8px;
    text-shadow: none;
}
/* 全屏时去除底部空白和外边距 */
.monitor-main-area:fullscreen,
.monitor-main-area:-webkit-full-screen,
.monitor-main-area:-moz-full-screen,
.monitor-main-area:-ms-fullscreen {
    padding: 0 !important;
    margin: 0 !important;
    height: 100vh !important;
    background: #000;
}
.video-grid-container:fullscreen,
.video-grid-container:-webkit-full-screen,
.video-grid-container:-moz-full-screen,
.video-grid-container:-ms-fullscreen {
    padding: 0 !important;
    margin: 0 !important;
    gap: 0 !important;
    height: 100vh !important;
    background: #000;
}

/* ===================== */
/* 自定义滚动条美化样式 */
/* ===================== */
.monitor-nav-content::-webkit-scrollbar {
    width: 10px;
    background: rgba(0, 40, 80, 0.18); /* 滚动条轨道背景 */
    border-radius: 8px;
}
.monitor-nav-content::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #00fff7 0%, #2196f3 100%);
    border-radius: 8px;
    box-shadow: 0 0 8px #00fff7, 0 0 16px #2196f3;
    border: 2px solid rgba(20,60,120,0.92);
}
.monitor-nav-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #00bcd4 0%, #2196f3 100%);
}
.monitor-nav-content::-webkit-scrollbar-corner {
    background: transparent;
}
.monitor-nav-content::-webkit-scrollbar-button {
    display: none;
    height: 0;
    width: 0;
}
/* 兼容 Firefox */
.monitor-nav-content {
    scrollbar-width: thin;
    scrollbar-color: #00fff7 #102040;
}
/* ===================== */

/* 设备标记样式 */
.custom-device-icon {
    background: transparent !important;
    border: none !important;
}

.device-marker {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
}

.device-marker:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.device-marker.offline {
    opacity: 0.6;
    background-color: #757575 !important;
}

/* 设备详细弹窗样式 */
.popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 10000;
    backdrop-filter: blur(3px);
}

/* 数据源信息样式 */
.data-source-info {
    background: rgba(0, 150, 255, 0.1);
    border: 1px solid rgba(0, 150, 255, 0.3);
    border-radius: 4px;
    padding: 8px 12px;
    margin-bottom: 15px;
    text-align: center;
}

.data-source-label {
    color: #0096ff;
    font-size: 12px;
    font-weight: 500;
}

.device-detail-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    z-index: 10001;
    width: 800px;
    height: 600px;
    overflow: hidden;
    border: 2px solid rgba(0, 255, 247, 0.4);
    display: flex;
    flex-direction: column;
}

.device-detail-header {
    background: rgba(0, 0, 0, 0.3);
    padding: 16px 20px;
    border-bottom: 1px solid rgba(0, 255, 247, 0.3);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
}

.device-detail-header h3 {
    color: #00fff7;
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.device-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.device-status.online {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
    border: 1px solid #4CAF50;
}

.device-status.offline {
    background: rgba(244, 67, 54, 0.2);
    color: #F44336;
    border: 1px solid #F44336;
}

/* 离线消息样式 */
.offline-message {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    background: rgba(255, 107, 107, 0.1);
    border-radius: 8px;
    margin: 20px 0;
}

.offline-message i {
    font-size: 48px;
    color: #ff6b6b;
    margin-bottom: 15px;
    display: block;
}

.offline-message p {
    margin: 10px 0;
    font-size: 16px;
}

.offline-time {
    color: #ff6b6b;
    font-size: 12px;
    margin-left: 10px;
    font-weight: bold;
}

/* 设备标记离线状态 */
.device-marker.offline {
    opacity: 0.6;
    filter: grayscale(50%);
    position: relative;
}

.device-marker.offline::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: #f44336;
    border-radius: 50%;
    border: 2px solid white;
    z-index: 10;
}

/* 区县设备标记样式 */
.device-marker-county {
    position: relative;
}

.device-marker-county .offline-indicator {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: #f44336;
    border-radius: 50%;
    border: 2px solid white;
    z-index: 10;
}

.device-marker-county.offline {
    opacity: 0.6;
    filter: grayscale(50%);
}

.popup-close {
    background: none;
    border: none;
    color: #00fff7;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s;
}

.refresh-btn {
    background: none;
    border: 1px solid rgba(0, 255, 247, 0.3);
    color: #00fff7;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
}

.refresh-btn:hover {
    background: rgba(0, 255, 247, 0.1);
    border-color: #00fff7;
}

.popup-close:hover {
    background: rgba(0, 255, 247, 0.1);
    transform: rotate(90deg);
}

.device-detail-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.device-basic-info {
    padding: 16px 20px;
    background: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(0, 255, 247, 0.2);
    display: flex;
    gap: 20px;
    flex-shrink: 0;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
}

.info-item .label {
    color: #ccc;
}

.info-item .value {
    color: #00fff7;
    font-weight: 500;
}

/* 标签页样式 */
.data-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.tab-header {
    display: flex;
    background: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(0, 255, 247, 0.2);
    flex-shrink: 0;
}

.tab-btn {
    flex: 1;
    padding: 12px 16px;
    background: none;
    border: none;
    color: #ccc;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 14px;
    border-bottom: 2px solid transparent;
}

.tab-btn.active {
    color: #00fff7;
    border-bottom-color: #00fff7;
    background: rgba(0, 255, 247, 0.1);
}

.tab-btn:hover:not(.active) {
    color: #00fff7;
    background: rgba(0, 255, 247, 0.05);
}

.tab-content {
    flex: 1;
    overflow: hidden;
}

.tab-pane {
    height: 100%;
    overflow-y: auto;
    padding: 16px 20px;
    display: none;
}

/* 弹窗滚动条样式 */
.tab-pane::-webkit-scrollbar {
    width: 8px;
    background: rgba(0, 40, 80, 0.3);
    border-radius: 6px;
}

.tab-pane::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #00fff7 0%, #2196f3 100%);
    border-radius: 6px;
    box-shadow: 0 0 6px #00fff7;
    border: 1px solid rgba(30, 60, 114, 0.8);
}

.tab-pane::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #00bcd4 0%, #1976d2 100%);
    box-shadow: 0 0 8px #00bcd4;
}

.tab-pane::-webkit-scrollbar-corner {
    background: transparent;
}

.tab-pane::-webkit-scrollbar-button {
    display: none;
}

/* Firefox 滚动条 */
.tab-pane {
    scrollbar-width: thin;
    scrollbar-color: #00fff7 rgba(0, 40, 80, 0.3);
}

.tab-pane.active {
    display: block;
}

/* 实时数据样式 */
.realtime-header {
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(0, 255, 247, 0.2);
}

.last-update {
    color: #ccc;
    font-size: 12px;
}

.realtime-data-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.realtime-data-item {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 6px;
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
    border-left: 3px solid #00fff7;
}

.data-icon {
    width: 32px;
    height: 32px;
    background: rgba(0, 255, 247, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #00fff7;
    font-size: 14px;
}

.data-content {
    flex: 1;
}

.data-label {
    display: block;
    color: #ccc;
    font-size: 12px;
    margin-bottom: 4px;
}

.data-value {
    display: block;
    color: #00fff7;
    font-size: 16px;
    font-weight: 600;
}

.value-number {
    color: #00fff7;
    font-size: 18px;
    font-weight: 700;
    margin-right: 4px;
}

.value-unit {
    color: #b0bec5;
    font-size: 13px;
    font-weight: 400;
}

/* 土壤数据样式 */
.soil-realtime-data {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.soil-section h5 {
    color: #00fff7;
    margin: 0 0 12px 0;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.soil-depth-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 8px;
}

/* 新的土壤分层数据样式 */
.soil-layers-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.soil-layer-card {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 12px;
    border: 1px solid rgba(0, 255, 247, 0.2);
}

.layer-header {
    color: #00fff7;
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 8px;
    text-align: center;
    padding-bottom: 6px;
    border-bottom: 1px solid rgba(0, 255, 247, 0.2);
}

.layer-params {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.layer-param {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

.layer-param i {
    color: #00fff7;
    width: 14px;
    text-align: center;
}

.layer-param .param-label {
    color: #ccc;
    flex: 1;
}

.layer-param .param-value {
    color: #00fff7;
    font-weight: 600;
}

.layer-param .value-number {
    color: #00fff7;
    font-size: 14px;
    font-weight: 700;
    margin-right: 3px;
}

.layer-param .value-unit {
    color: #b0bec5;
    font-size: 11px;
    font-weight: 400;
}

.soil-overall-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
}

.soil-overall-item {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 6px;
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
    border-left: 3px solid #00fff7;
}

.soil-depth-item {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    padding: 8px;
    text-align: center;
    border: 1px solid rgba(0, 255, 247, 0.2);
}

.depth-label {
    display: block;
    color: #ccc;
    font-size: 11px;
    margin-bottom: 4px;
}

.depth-value {
    display: block;
    color: #00fff7;
    font-size: 13px;
    font-weight: 600;
}

.soil-other-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
}

.soil-other-item {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 6px;
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
    border-left: 3px solid #00fff7;
}

.param-icon {
    width: 28px;
    height: 28px;
    background: rgba(0, 255, 247, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #00fff7;
    font-size: 12px;
}

.param-content {
    flex: 1;
}

.param-label {
    display: block;
    color: #ccc;
    font-size: 11px;
    margin-bottom: 2px;
}

.param-value {
    display: block;
    color: #00fff7;
    font-size: 14px;
    font-weight: 600;
}

/* 孢子和虫情数据样式 */
.spore-summary, .pest-summary {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    margin-bottom: 16px;
}

.summary-card {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 6px;
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
    border-left: 3px solid #00fff7;
}

.summary-card.risk-高 {
    border-left-color: #F44336;
}

.summary-card.risk-中等 {
    border-left-color: #FF9800;
}

.summary-card.risk-低 {
    border-left-color: #4CAF50;
}

.summary-icon {
    width: 32px;
    height: 32px;
    background: rgba(0, 255, 247, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #00fff7;
    font-size: 14px;
}

.summary-content {
    flex: 1;
}

.summary-label {
    display: block;
    color: #ccc;
    font-size: 11px;
    margin-bottom: 4px;
}

.summary-value {
    display: block;
    color: #00fff7;
    font-size: 16px;
    font-weight: 600;
}

/* 图片网格样式 */
.image-grid-section h5 {
    color: #00fff7;
    margin: 0 0 12px 0;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.image-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
}

.image-grid-item {
    background: rgba(0, 0, 0, 0.4);
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 255, 247, 0.3);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.image-grid-item:hover {
    transform: translateY(-3px) scale(1.02);
    border-color: #00fff7;
    box-shadow: 0 6px 20px rgba(0, 255, 247, 0.3), 0 0 15px rgba(0, 255, 247, 0.2);
}

.image-thumbnail {
    width: 100%;
    height: 100px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 40, 80, 0.4) 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #00fff7;
    border-bottom: 1px solid rgba(0, 255, 247, 0.3);
    overflow: hidden;
    position: relative;
}

.image-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.image-grid-item:hover .image-thumbnail img {
    transform: scale(1.05);
}

/* 图片加载失败时的样式 */
.image-thumbnail.image-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(0, 40, 80, 0.8) 0%, rgba(30, 60, 114, 0.6) 100%);
    border: 2px dashed rgba(0, 255, 247, 0.4);
}

.image-error-placeholder {
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #00fff7;
    text-align: center;
    font-size: 12px;
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
}

.image-thumbnail.image-error .image-error-placeholder {
    display: flex !important;
}

.image-error-placeholder i {
    font-size: 32px;
    margin-bottom: 6px;
    opacity: 0.8;
    color: #00fff7;
    text-shadow: 0 0 8px rgba(0, 255, 247, 0.5);
}

.image-error-placeholder span {
    font-size: 11px;
    color: #b0bec5;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.image-thumbnail i {
    font-size: 24px;
    margin-bottom: 4px;
}

.image-placeholder-text {
    font-size: 10px;
    color: #ccc;
}

.image-meta {
    padding: 6px 8px;
    background: linear-gradient(90deg, rgba(0, 40, 80, 0.8) 0%, rgba(0, 60, 120, 0.6) 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 2px;
}

.image-time {
    font-size: 10px;
    color: #00fff7;
    font-weight: 500;
    text-align: center;
    letter-spacing: 0.3px;
    text-shadow: 0 0 4px rgba(0, 255, 247, 0.3);
    line-height: 1.2;
}

.image-count {
    font-size: 10px;
    color: #00fff7;
    font-weight: 500;
}

/* 图片详情查看器 */
.image-detail-viewer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 20000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-detail-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 19999;
}

.image-detail-content {
    position: relative;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    max-width: 90%;
    max-height: 90%;
    display: flex;
    flex-direction: column;
}

.image-detail-container {
    width: 850px;
    height: 550px;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.image-detail-container img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.image-detail-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    z-index: 20001;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-detail-info {
    padding: 16px;
    background: #f9f9f9;
    border-top: 1px solid #ddd;
}

.image-detail-info h4 {
    margin: 0 0 12px 0;
    color: #333;
    font-size: 16px;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.detail-label {
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

.detail-value {
    font-size: 14px;
    color: #333;
    font-weight: 600;
}

/* 加载和错误状态 */
.loading, .no-data {
    text-align: center;
    color: #00fff7;
    padding: 40px 20px;
    font-size: 14px;
}

.no-data {
    color: #ccc;
}

/* 旋转动画 */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.device-info {
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    border-left: 4px solid #00fff7;
}

.device-info p {
    margin: 8px 0;
    font-size: 14px;
}

.device-info strong {
    color: #00fff7;
}

/* 数据显示样式 */
.data-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(0, 255, 247, 0.3);
}

.data-header h4 {
    color: #00fff7;
    margin: 0;
    font-size: 18px;
}

.last-update {
    font-size: 12px;
    color: #ccc;
}

.data-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.data-item {
    background: rgba(0, 0, 0, 0.3);
    padding: 12px;
    border-radius: 6px;
    border-left: 3px solid #00fff7;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.data-item .label {
    font-size: 13px;
    color: #ccc;
}

.data-item .value {
    font-size: 16px;
    font-weight: 600;
    color: #00fff7;
}

.data-section {
    margin-bottom: 20px;
}

.data-section h5 {
    color: #00fff7;
    margin: 0 0 10px 0;
    font-size: 16px;
    padding-bottom: 5px;
    border-bottom: 1px solid rgba(0, 255, 247, 0.2);
}

/* 图片和特殊数据样式 */
.data-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
}

.summary-item {
    background: rgba(0, 0, 0, 0.3);
    padding: 10px;
    border-radius: 6px;
    text-align: center;
    border: 1px solid rgba(0, 255, 247, 0.2);
}

.summary-item .label {
    display: block;
    font-size: 12px;
    color: #ccc;
    margin-bottom: 5px;
}

.summary-item .value {
    font-size: 18px;
    font-weight: 600;
    color: #00fff7;
}

.risk-高 { color: #F44336; }
.risk-中等 { color: #FF9800; }
.risk-低 { color: #4CAF50; }

.image-list {
    margin-top: 20px;
}

.image-list h5 {
    color: #00fff7;
    margin: 0 0 15px 0;
    font-size: 16px;
}

.image-item {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid rgba(0, 255, 247, 0.2);
}

.image-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.capture-time {
    font-size: 13px;
    color: #ccc;
}

.spore-count, .pest-count {
    font-size: 12px;
    color: #ffd700;
    font-weight: 500;
    text-align: center;
    letter-spacing: 0.2px;
    text-shadow: 0 0 3px rgba(255, 215, 0, 0.3);
    line-height: 1.2;
}

/* 离线时间显示样式 */
.offline-time-display {
    color: #ff6b6b;
    font-size: 12px;
    font-weight: 500;
    margin-left: 10px;
    text-shadow: 0 0 3px rgba(255, 107, 107, 0.3);
}

/* 返回首页按钮样式 */
.home-btn {
    position: absolute;
    top: 15px;
    left: 30px;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1001;
    box-shadow: 0 3px 12px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.home-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 18px rgba(102, 126, 234, 0.6);
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.home-btn i {
    color: #ffffff;
    font-size: 16px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 下钻页面隐藏返回首页按钮 */
.home-btn.hidden {
    display: none;
}

/* 智能孢子仪专用样式 */
.spore-realtime-data {
    padding: 0;
}

.spore-stats-section {
    margin-bottom: 20px;
}

.spore-stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-top: 10px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: rgba(0, 40, 80, 0.3);
    border: 1px solid rgba(0, 255, 247, 0.2);
    border-radius: 8px;
}

.stat-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 255, 247, 0.1);
    border-radius: 50%;
    color: #00fff7;
    font-size: 18px;
}

.stat-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.stat-label {
    font-size: 12px;
    color: #b3e5fc;
}

.stat-value {
    font-size: 16px;
    color: #ffffff;
    font-weight: 600;
}

.image-grid-section h5 {
    color: #00fff7;
    font-size: 14px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.image-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
}

.image-grid-item {
    background: rgba(0, 20, 40, 0.4);
    border: 1px solid rgba(0, 255, 247, 0.2);
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s;
}

.image-grid-item:hover {
    border-color: #00fff7;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 255, 247, 0.2);
}

.image-thumbnail {
    position: relative;
    width: 100%;
    height: 120px;
    overflow: hidden;
}

.image-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-error-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(0, 40, 80, 0.8);
    color: #00fff7;
    font-size: 12px;
}

.image-error-placeholder i {
    font-size: 24px;
    margin-bottom: 8px;
}

.image-meta {
    padding: 10px;
}

.image-time {
    font-size: 11px;
    color: #b3e5fc;
    margin-bottom: 4px;
}

.no-images {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.no-images i {
    font-size: 48px;
    margin-bottom: 15px;
    color: #444;
}

.no-images p {
    font-size: 14px;
    margin: 0;
}

/* 图片查看器样式 */
.image-viewer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease;
}

.image-viewer-container {
    max-width: 90%;
    max-height: 90%;
    background: rgba(30, 60, 114, 0.95);
    border: 2px solid #00fff7;
    border-radius: 12px;
    overflow: hidden;
    animation: slideIn 0.3s ease;
}

.image-viewer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: rgba(0, 255, 247, 0.1);
    border-bottom: 1px solid rgba(0, 255, 247, 0.2);
}

.image-viewer-header h4 {
    color: #00fff7;
    margin: 0;
    font-size: 16px;
}

.image-viewer-close {
    background: none;
    border: none;
    color: #00fff7;
    font-size: 24px;
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s;
}

.image-viewer-close:hover {
    background: rgba(0, 255, 247, 0.2);
}

.image-viewer-content {
    padding: 20px;
    text-align: center;
}

.image-viewer-content img {
    max-width: 100%;
    max-height: 70vh;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.spore-types {
    font-size: 12px;
    color: #ccc;
    margin-bottom: 10px;
    padding: 5px 10px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
}

.pest-details {
    margin-bottom: 10px;
}

.pest-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 10px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    margin-bottom: 5px;
    font-size: 12px;
}

.pest-name {
    color: #00fff7;
    font-weight: 500;
}

.harm-level {
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
}

.harm-level.高 {
    background: rgba(244, 67, 54, 0.2);
    color: #F44336;
}

.harm-level.中等 {
    background: rgba(255, 152, 0, 0.2);
    color: #FF9800;
}

.harm-level.低 {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
}

.image-placeholder {
    background: rgba(0, 0, 0, 0.4);
    border: 2px dashed rgba(0, 255, 247, 0.3);
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
}

.image-placeholder:hover {
    border-color: #00fff7;
    background: rgba(0, 255, 247, 0.1);
}

.image-placeholder i {
    font-size: 24px;
    color: #00fff7;
    margin-bottom: 8px;
    display: block;
}

.image-placeholder span {
    color: #ccc;
    font-size: 12px;
}

/* 报警信息样式 */
.alarms-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid rgba(0, 255, 247, 0.3);
}

.alarms-section h5 {
    color: #F44336;
    margin: 0 0 15px 0;
    font-size: 16px;
}

.alarm-item {
    display: flex;
    align-items: flex-start;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 10px;
    border-left: 4px solid;
}

.alarm-item.warning {
    background: rgba(255, 152, 0, 0.1);
    border-left-color: #FF9800;
}

.alarm-item.error {
    background: rgba(244, 67, 54, 0.1);
    border-left-color: #F44336;
}

.alarm-item i {
    color: #FF9800;
    margin-right: 10px;
    margin-top: 2px;
}

.alarm-content {
    flex: 1;
}

.alarm-message {
    display: block;
    color: white;
    font-size: 13px;
    margin-bottom: 4px;
}

.alarm-time {
    font-size: 11px;
    color: #ccc;
}

/* 图片查看器样式 */
.image-viewer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 20000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-viewer-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    background: white;
    border-radius: 8px;
    overflow: hidden;
}

.image-viewer-content img {
    width: 100%;
    height: auto;
    display: block;
}

.image-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    z-index: 20001;
}

.image-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 19999;
}

.image-info {
    padding: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    font-size: 12px;
}

/* 加载和错误状态 */
.loading {
    text-align: center;
    color: #00fff7;
    padding: 40px;
    font-size: 16px;
}

.error-message {
    text-align: center;
    color: #F44336;
    padding: 20px;
    background: rgba(244, 67, 54, 0.1);
    border-radius: 6px;
    border: 1px solid rgba(244, 67, 54, 0.3);
}

.error-message i {
    font-size: 24px;
    margin-bottom: 10px;
    display: block;
}

.error-message button {
    background: #F44336;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
    transition: background 0.3s;
}

.error-message button:hover {
    background: #d32f2f;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .device-popup {
        width: 95%;
        max-height: 90vh;
    }

    .data-grid {
        grid-template-columns: 1fr;
    }

    .data-summary {
        grid-template-columns: repeat(2, 1fr);
    }

    .image-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}

/* 新增样式：设备状态指示器 */
.device-status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-left: auto;
    flex-shrink: 0;
}

.monitor-device-item.online .device-status-indicator {
    background: #4caf50;
    box-shadow: 0 0 6px #4caf50;
}

.monitor-device-item.offline .device-status-indicator {
    background: #f44336;
    box-shadow: 0 0 6px #f44336;
}



/* 云台控制指示器 */
.ptz-indicator {
    color: #4caf50;
    font-size: 12px;
    margin-left: auto;
    margin-right: 8px;
}

/* 搜索高亮 */
.monitor-device-item.search-highlight {
    background: rgba(255, 193, 7, 0.2);
    border-color: #ffc107;
    animation: searchPulse 1s ease-in-out infinite alternate;
}

@keyframes searchPulse {
    from { box-shadow: 0 0 5px rgba(255, 193, 7, 0.5); }
    to { box-shadow: 0 0 15px rgba(255, 193, 7, 0.8); }
}

/* 云台控制面板禁用状态 */
.monitor-ptz-panel.disabled {
    opacity: 0.5;
    pointer-events: none;
}

.monitor-ptz-panel.disabled .ptz-btn {
    cursor: not-allowed;
    background: rgba(0, 40, 80, 0.3) !important;
    border-color: rgba(0, 255, 247, 0.3) !important;
    color: rgba(0, 255, 247, 0.3) !important;
}

/* 加载和错误状态 */
.loading-placeholder {
    text-align: center;
    color: #00fff7;
    padding: 40px 20px;
    font-size: 16px;
}

.error-message {
    text-align: center;
    color: #f44336;
    padding: 40px 20px;
}

.error-message i {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
}

.error-message p {
    margin: 16px 0;
    font-size: 16px;
}

.retry-btn {
    background: #00fff7;
    color: #003366;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
}

.retry-btn:hover {
    background: #00bcd4;
    transform: translateY(-2px);
}

/* 通知样式 */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.notification {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* 设备状态样式增强 */
.monitor-device-item.offline {
    opacity: 0.6;
    background: rgba(244, 67, 54, 0.1);
    border-color: rgba(244, 67, 54, 0.3);
}



.monitor-device-item.offline:hover {
    background: rgba(244, 67, 54, 0.2);
    border-color: rgba(244, 67, 54, 0.5);
}



/* 响应式优化 */
@media (max-width: 768px) {
    .ptz-indicator {
        display: none;
    }

    .device-status-indicator {
        width: 6px;
        height: 6px;
    }

    .notification {
        right: 10px;
        left: 10px;

/* ViewerJS z-index 覆盖 */
.viewer-container {
    z-index: 50000 !important;
}

.viewer-backdrop {
    z-index: 49999 !important;
}

.viewer-canvas {
    z-index: 50001 !important;
}

.viewer-navbar {
    z-index: 50002 !important;
}

.viewer-toolbar {
    z-index: 50003 !important;
}

.viewer-tooltip {
    z-index: 50004 !important;
}

.viewer-button {
    z-index: 50005 !important;
}

.viewer-player {
    z-index: 50006 !important;
}
        top: 10px;
        font-size: 14px;
        padding: 10px 16px;
    }
}

/* 简化设备弹窗样式（无devBid的虚拟设备） */
.simple-device-popup .leaflet-popup-content-wrapper {
    background: transparent !important;
    border: none !important;
    border-radius: 12px !important;
    box-shadow: none !important;
    padding: 0 !important;
}

.simple-device-popup .leaflet-popup-content {
    margin: 0 !important;
    padding: 0 !important;
    color: white !important;
    font-family: 'Microsoft YaHei', Arial, sans-serif !important;
}

.simple-device-popup .leaflet-popup-tip {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
    border: 2px solid #00fff7 !important;
}